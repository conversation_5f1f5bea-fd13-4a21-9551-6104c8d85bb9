/**
 * 智能选择器系统
 * 基于组件结构和语义化选择器，无需手动添加data-tour属性
 */

export interface SmartSelectorConfig {
  /** 选择器优先级列表，按优先级从高到低排列 */
  selectors: string[];
  /** 选择器描述 */
  description: string;
  /** 验证函数，用于确认找到的元素是否正确 */
  validator?: (element: Element) => boolean;
}

/**
 * 智能选择器配置映射
 * 基于页面功能和组件结构自动生成选择器
 */
export const smartSelectors: { [key: string]: SmartSelectorConfig } = {
  // 聊天输入框
  'chat-input': {
    selectors: [
      '.ql-editor', // Quill编辑器
      'textarea[placeholder*="输入"]',
      'input[placeholder*="输入"]',
      '[role="textbox"]',
      '.mantine-Textarea-input',
      'textarea',
    ],
    description: '聊天输入框',
    validator: (el) => {
      const rect = el.getBoundingClientRect();
      return rect.height > 30 && rect.width > 200; // 确保是主要的输入框
    },
  },

  // 发送按钮
  'chat-send': {
    selectors: [
      'button[aria-label*="发送"]',
      'button[type="submit"]:has(svg)',
      '.mantine-Button-root:has([aria-label*="发送"])',
      'button:has(svg):last-of-type',
      '.mantine-ActionIcon-root:last-child',
    ],
    description: '发送按钮',
    validator: (el) => {
      const text = el.textContent?.toLowerCase() || '';
      const ariaLabel = el.getAttribute('aria-label')?.toLowerCase() || '';
      return text.includes('发送') || ariaLabel.includes('发送') || el.querySelector('svg') !== null;
    },
  },

  // 分类标签
  'category-tabs': {
    selectors: [
      '.mantine-Tabs-list',
      '.filter-tabs',
      '[role="tablist"]',
      '.mantine-SegmentedControl-root',
      '.category-filter',
    ],
    description: '分类标签栏',
    validator: (el) => {
      const tabs = el.querySelectorAll('[role="tab"], .mantine-Tabs-tab, .mantine-SegmentedControl-control');
      return tabs.length >= 2; // 至少有2个标签
    },
  },

  // 卡片组件
  'module-card': {
    selectors: [
      '.mantine-UnstyledButton-root:has(.mantine-Image-root)',
      '.module-card',
      '.agent-card',
      '.course-card',
      '.mantine-Card-root:has(.mantine-Image-root)',
      '.mantine-Paper-root:has(img)',
    ],
    description: '功能卡片',
    validator: (el) => {
      const hasImage = el.querySelector('img, .mantine-Image-root') !== null;
      const hasText = el.textContent && el.textContent.trim().length > 0;
      return hasImage && hasText;
    },
  },

  // 创建按钮
  'create-button': {
    selectors: [
      'button:contains("创建")',
      '.mantine-Button-root:contains("创建")',
      '.mantine-Card-root:has(.mantine-Text-root:contains("创建"))',
      '.upload-button',
      'button[type="button"]:has(.mantine-Text-root:contains("创建"))',
    ],
    description: '创建按钮',
    validator: (el) => {
      const text = el.textContent?.toLowerCase() || '';
      return text.includes('创建') || text.includes('上传') || text.includes('新建');
    },
  },

  // 导航菜单项
  'nav-item': {
    selectors: [
      '.mantine-AppShell-section a',
      'nav a',
      '.mantine-UnstyledButton-root:has(.mantine-Text-root)',
      '.menu-item',
      '[role="menuitem"]',
    ],
    description: '导航菜单项',
    validator: (el) => {
      const rect = el.getBoundingClientRect();
      return rect.width > 50 && rect.height > 20; // 确保是可见的菜单项
    },
  },
};

/**
 * 智能查找元素
 * @param selectorKey 选择器配置键
 * @param fallbackSelector 备用选择器
 * @returns 找到的元素或null
 */
export const findElementSmart = (selectorKey: string, fallbackSelector?: string): Element | null => {
  const config = smartSelectors[selectorKey];
  
  if (!config) {
    console.warn(`未找到选择器配置: ${selectorKey}`);
    return fallbackSelector ? document.querySelector(fallbackSelector) : null;
  }

  // 按优先级尝试每个选择器
  for (const selector of config.selectors) {
    try {
      const elements = document.querySelectorAll(selector);
      
      for (const element of Array.from(elements)) {
        // 检查元素是否可见
        const rect = element.getBoundingClientRect();
        if (rect.width === 0 || rect.height === 0) continue;
        
        // 检查元素是否在视口内或接近视口
        if (rect.bottom < -100 || rect.top > window.innerHeight + 100) continue;
        
        // 应用验证函数
        if (config.validator && !config.validator(element)) continue;
        
        console.log(`智能选择器找到元素: ${selectorKey} -> ${selector}`);
        return element;
      }
    } catch (error) {
      console.warn(`选择器执行失败: ${selector}`, error);
      continue;
    }
  }

  // 如果所有智能选择器都失败，尝试备用选择器
  if (fallbackSelector) {
    try {
      const element = document.querySelector(fallbackSelector);
      if (element) {
        console.log(`使用备用选择器找到元素: ${selectorKey} -> ${fallbackSelector}`);
        return element;
      }
    } catch (error) {
      console.warn(`备用选择器执行失败: ${fallbackSelector}`, error);
    }
  }

  console.warn(`未找到元素: ${selectorKey}`);
  return null;
};

/**
 * 批量查找元素
 * @param selectorKeys 选择器配置键数组
 * @returns 找到的元素映射
 */
export const findElementsBatch = (selectorKeys: string[]): Map<string, Element | null> => {
  const results = new Map<string, Element | null>();
  
  for (const key of selectorKeys) {
    results.set(key, findElementSmart(key));
  }
  
  return results;
};

/**
 * 等待元素出现
 * @param selectorKey 选择器配置键
 * @param timeout 超时时间（毫秒）
 * @param fallbackSelector 备用选择器
 * @returns Promise<Element | null>
 */
export const waitForElement = (
  selectorKey: string, 
  timeout: number = 5000,
  fallbackSelector?: string
): Promise<Element | null> => {
  return new Promise((resolve) => {
    const startTime = Date.now();
    
    const check = () => {
      const element = findElementSmart(selectorKey, fallbackSelector);
      
      if (element) {
        resolve(element);
        return;
      }
      
      if (Date.now() - startTime >= timeout) {
        console.warn(`等待元素超时: ${selectorKey}`);
        resolve(null);
        return;
      }
      
      // 使用 requestAnimationFrame 而不是 setTimeout 以获得更好的性能
      requestAnimationFrame(check);
    };
    
    check();
  });
};
