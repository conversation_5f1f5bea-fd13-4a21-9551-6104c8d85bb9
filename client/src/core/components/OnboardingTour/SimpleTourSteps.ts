import { Step } from 'react-joyride';

/**
 * 简化的引导步骤配置
 * 使用基本选择器，确保稳定性
 */

export const simpleTourSteps: { [key: string]: Step[] } = {
  // 对话页面引导
  chat: [
    {
      target: '.ql-editor',
      content: '在这里输入您想要询问的问题。按 Shift+Enter 可以发送。',
      placement: 'top',
      disableBeacon: true,
    },
    {
      target: '.mantine-ActionIcon-root:last-child',
      content: '点击发送按钮或按 Enter 键发送消息。',
      placement: 'top',
    },
  ],

  // 智能体市场引导
  agent: [
    {
      target: '.mantine-Tabs-list',
      content: '浏览不同类别的智能体，找到最适合您需求的AI助手。',
      placement: 'bottom',
      disableBeacon: true,
    },
    {
      target: '.mantine-UnstyledButton-root:first-of-type',
      content: '点击智能体卡片查看详情或购买使用。',
      placement: 'top',
    },
  ],

  // 课程页面引导
  course: [
    {
      target: '.mantine-Tabs-list',
      content: '选择您感兴趣的课程类别，系统学习相关知识。',
      placement: 'bottom',
      disableBeacon: true,
    },
    {
      target: '.mantine-UnstyledButton-root:first-of-type',
      content: '查看课程介绍和价格信息，选择适合的课程开始学习。',
      placement: 'top',
    },
  ],

  // 工作流页面引导
  workflow: [
    {
      target: '.mantine-Tabs-list',
      content: '浏览不同类别的工作流，找到适合您业务需求的自动化流程。',
      placement: 'bottom',
      disableBeacon: true,
    },
    {
      target: '.mantine-UnstyledButton-root:first-of-type',
      content: '点击工作流卡片查看详情，了解自动化流程的功能和使用方法。',
      placement: 'top',
    },
  ],

  // 知识库页面引导
  knowledge: [
    {
      target: '.mantine-Card-root:first-child',
      content: '上传您的文档、资料，构建个人专属知识库。',
      placement: 'bottom',
      disableBeacon: true,
    },
    {
      target: '.mantine-Card-root:nth-child(2)',
      content: '管理您的知识库内容，支持搜索、编辑和删除操作。',
      placement: 'top',
    },
  ],

  // 个人中心引导
  ucenter: [
    {
      target: 'nav a:first-child',
      content: '查看您购买的智能体和课程，管理您的数字资产。',
      placement: 'right',
      disableBeacon: true,
    },
    {
      target: 'nav a:nth-child(2)',
      content: '查看您的使用情况和消费记录，了解服务使用详情。',
      placement: 'right',
    },
    {
      target: 'nav a:nth-child(3)',
      content: '管理您的账号信息，包括个人资料和安全设置。',
      placement: 'right',
    },
  ],
};

/**
 * 验证元素是否存在且可见
 * @param selector 选择器
 * @returns 是否有效
 */
export const isElementValid = (selector: string): boolean => {
  try {
    const element = document.querySelector(selector);
    if (!element) return false;

    const rect = element.getBoundingClientRect();
    return rect.width > 0 && rect.height > 0;
  } catch (error) {
    console.warn(`选择器验证失败: ${selector}`, error);
    return false;
  }
};

/**
 * 获取页面的有效引导步骤
 * @param page 页面类型
 * @returns 有效的步骤数组
 */
export const getValidSteps = (page: string): Step[] => {
  const steps = simpleTourSteps[page];
  if (!steps) {
    console.warn(`页面 ${page} 没有配置引导步骤`);
    return [];
  }

  // 过滤出有效的步骤
  const validSteps = steps.filter(step => {
    const isValid = isElementValid(step.target as string);
    if (!isValid) {
      console.warn(`步骤目标元素不存在或不可见: ${step.target}`);
    }
    return isValid;
  });

  console.log(`页面 ${page} 有效步骤数: ${validSteps.length}/${steps.length}`);
  return validSteps;
};

/**
 * 等待元素出现
 * @param selector 选择器
 * @param timeout 超时时间
 * @returns Promise<boolean>
 */
export const waitForElement = (selector: string, timeout: number = 3000): Promise<boolean> => {
  return new Promise((resolve) => {
    const startTime = Date.now();
    
    const check = () => {
      if (isElementValid(selector)) {
        resolve(true);
        return;
      }
      
      if (Date.now() - startTime >= timeout) {
        resolve(false);
        return;
      }
      
      setTimeout(check, 100);
    };
    
    check();
  });
};

/**
 * 等待页面准备就绪
 * @param page 页面类型
 * @returns Promise<boolean>
 */
export const waitForPageReady = async (page: string): Promise<boolean> => {
  const steps = simpleTourSteps[page];
  if (!steps || steps.length === 0) {
    return true;
  }

  // 等待第一个步骤的元素出现
  const firstStep = steps[0];
  const isReady = await waitForElement(firstStep.target as string, 5000);
  
  if (!isReady) {
    console.warn(`页面 ${page} 未准备就绪，第一个元素未找到: ${firstStep.target}`);
  }
  
  return isReady;
};
