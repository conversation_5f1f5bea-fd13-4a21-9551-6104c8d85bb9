# 智能引导系统

## 概述

新的智能引导系统完全移除了对手动 `data-tour` 属性的依赖，采用智能选择器自动识别页面元素。

## 主要优势

1. **零侵入性**: 无需在组件中添加任何 `data-tour` 属性
2. **自动适配**: 基于组件结构和语义化选择器自动识别元素
3. **布局安全**: 不会因为包装元素而破坏页面布局
4. **易于维护**: 集中配置，无需在多个组件中同步更新
5. **智能验证**: 自动验证元素可见性和可用性

## 核心组件

### 1. SmartSelector.ts
智能选择器系统，定义了各种UI元素的识别策略：

```typescript
// 示例：聊天输入框的智能选择器
'chat-input': {
  selectors: [
    '.ql-editor',                    // Quill编辑器
    'textarea[placeholder*="输入"]',  // 包含"输入"的文本框
    '[role="textbox"]',              // 语义化文本框
    '.mantine-Textarea-input',       // Mantine组件
  ],
  validator: (el) => {
    const rect = el.getBoundingClientRect();
    return rect.height > 30 && rect.width > 200;
  }
}
```

### 2. SmartTourSteps.ts
智能引导步骤配置，使用选择器键而非具体选择器：

```typescript
chat: [
  {
    smartTarget: 'chat-input',
    content: '在这里输入您想要询问的问题。',
    placement: 'top',
    waitForElement: true,
  },
  {
    smartTarget: 'chat-send',
    content: '点击发送按钮发送消息。',
    placement: 'top',
  },
]
```

### 3. EnhancedOnboardingTour.tsx
增强的引导组件，自动处理智能选择器转换和元素验证。

## 支持的智能选择器

| 选择器键 | 描述 | 适用页面 |
|---------|------|---------|
| `chat-input` | 聊天输入框 | 对话页面 |
| `chat-send` | 发送按钮 | 对话页面 |
| `category-tabs` | 分类标签栏 | 市场页面 |
| `module-card` | 功能卡片 | 市场页面 |
| `create-button` | 创建按钮 | 知识库页面 |
| `nav-item` | 导航菜单项 | 个人中心 |

## 页面配置

当前支持的页面类型：
- `chat`: 对话页面
- `agent`: 智能体市场
- `course`: 课程市场  
- `workflow`: 工作流市场
- `knowledge`: 知识库
- `ucenter`: 个人中心

## 调试工具

开发环境下提供了调试器，可以：
- 查看当前页面的引导配置
- 测试智能选择器的匹配结果
- 手动启动/停止引导
- 查看生成的步骤详情

使用方法：
1. 在开发环境中按 `Ctrl+Shift+D` 打开调试器
2. 点击"收集调试信息"查看详细信息
3. 使用"启动引导"测试当前页面的引导流程

## 添加新页面引导

1. **在 SmartSelector.ts 中添加新的选择器**（如果需要）：
```typescript
'new-element': {
  selectors: [
    '.specific-class',
    '[data-testid="element"]',
    '.fallback-selector'
  ],
  description: '新元素描述',
  validator: (el) => {
    // 验证逻辑
    return true;
  }
}
```

2. **在 SmartTourSteps.ts 中配置页面步骤**：
```typescript
newpage: [
  {
    smartTarget: 'new-element',
    content: '引导内容',
    placement: 'bottom',
    waitForElement: true,
  }
]
```

3. **在 tourSteps.ts 中添加页面类型映射**：
```typescript
export const getPageTypeFromPath = (pathname: string): string => {
  // ... 其他映射
  if (pathname.startsWith('/newpage')) return 'newpage';
  return '';
};
```

## 最佳实践

1. **选择器优先级**: 将最具体的选择器放在前面，通用选择器作为备选
2. **验证函数**: 使用验证函数确保找到的是正确的元素
3. **等待机制**: 对于动态加载的元素，启用 `waitForElement`
4. **备用选择器**: 为关键步骤提供 `fallbackTarget`
5. **测试验证**: 使用调试器验证所有页面的引导流程

## 迁移指南

从旧的 `data-tour` 系统迁移：

1. **移除所有 `data-tour` 属性**
2. **检查是否有布局问题**（特别是不必要的包装div）
3. **使用调试器验证新系统**
4. **根据需要调整智能选择器配置**

## 故障排除

常见问题：
- **元素未找到**: 检查选择器配置和元素渲染时机
- **引导不启动**: 确认页面类型映射正确
- **步骤跳过**: 查看控制台警告信息
- **布局问题**: 确认移除了所有不必要的包装元素

使用调试器可以快速定位和解决大部分问题。
