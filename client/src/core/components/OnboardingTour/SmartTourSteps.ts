import { Step } from 'react-joyride';
import { findElementSmart, waitForElement } from './SmartSelector';

/**
 * 智能引导步骤配置
 * 使用智能选择器系统，无需手动添加data-tour属性
 */

// 定义智能步骤接口
interface SmartStep extends Omit<Step, 'target'> {
  /** 智能选择器键 */
  smartTarget: string;
  /** 备用选择器 */
  fallbackTarget?: string;
  /** 是否等待元素出现 */
  waitForElement?: boolean;
  /** 等待超时时间 */
  waitTimeout?: number;
}

// 智能步骤配置
const smartTourSteps: { [key: string]: SmartStep[] } = {
  // 对话页面引导
  chat: [
    {
      smartTarget: 'chat-input',
      content: '在这里输入您想要询问的问题。按 Shift+Enter 可以发送。',
      placement: 'top',
      disableBeacon: true,
      waitForElement: true,
    },
    {
      smartTarget: 'chat-send',
      content: '点击发送按钮或按 Enter 键发送消息。',
      placement: 'top',
      waitForElement: true,
    },
  ],

  // 智能体市场引导
  agent: [
    {
      smartTarget: 'category-tabs',
      content: '浏览不同类别的智能体，找到最适合您需求的AI助手。',
      placement: 'bottom',
      disableBeacon: true,
      waitForElement: true,
    },
    {
      smartTarget: 'module-card',
      content: '点击智能体卡片查看详情或购买使用。',
      placement: 'top',
      waitForElement: true,
    },
  ],

  // 课程页面引导
  course: [
    {
      smartTarget: 'category-tabs',
      content: '选择您感兴趣的课程类别，系统学习相关知识。',
      placement: 'bottom',
      disableBeacon: true,
      waitForElement: true,
    },
    {
      smartTarget: 'module-card',
      content: '查看课程介绍和价格信息，选择适合的课程开始学习。',
      placement: 'top',
      waitForElement: true,
    },
  ],

  // 工作流页面引导
  workflow: [
    {
      smartTarget: 'category-tabs',
      content: '浏览不同类别的工作流，找到适合您业务需求的自动化流程。',
      placement: 'bottom',
      disableBeacon: true,
      waitForElement: true,
    },
    {
      smartTarget: 'module-card',
      content: '点击工作流卡片查看详情，了解自动化流程的功能和使用方法。',
      placement: 'top',
      waitForElement: true,
    },
  ],

  // 知识库页面引导
  knowledge: [
    {
      smartTarget: 'create-button',
      content: '上传您的文档、资料，构建个人专属知识库。',
      placement: 'bottom',
      disableBeacon: true,
      waitForElement: true,
    },
    {
      smartTarget: 'module-card',
      fallbackTarget: '.mantine-SimpleGrid-root .mantine-Card-root:not(:first-child)',
      content: '管理您的知识库内容，支持搜索、编辑和删除操作。',
      placement: 'top',
      waitForElement: true,
    },
  ],

  // 个人中心引导
  ucenter: [
    {
      smartTarget: 'nav-item',
      fallbackTarget: 'nav a[href*="assets"]',
      content: '查看您购买的智能体和课程，管理您的数字资产。',
      placement: 'right',
      disableBeacon: true,
      waitForElement: true,
    },
    {
      smartTarget: 'nav-item',
      fallbackTarget: 'nav a[href*="usage"]',
      content: '查看您的使用情况和消费记录，了解服务使用详情。',
      placement: 'right',
      waitForElement: true,
    },
    {
      smartTarget: 'nav-item',
      fallbackTarget: 'nav a[href*="account"]',
      content: '管理您的账号信息，包括个人资料和安全设置。',
      placement: 'right',
      waitForElement: true,
    },
  ],
};

/**
 * 将智能步骤转换为标准步骤
 * @param smartSteps 智能步骤数组
 * @returns Promise<Step[]> 标准步骤数组
 */
export const convertSmartStepsToSteps = async (smartSteps: SmartStep[]): Promise<Step[]> => {
  const steps: Step[] = [];

  for (const smartStep of smartSteps) {
    const { smartTarget, fallbackTarget, waitForElement: shouldWait, waitTimeout = 3000, ...stepProps } = smartStep;

    let targetElement: Element | null = null;

    if (shouldWait) {
      // 等待元素出现
      targetElement = await waitForElement(smartTarget, waitTimeout, fallbackTarget);
    } else {
      // 立即查找元素
      targetElement = findElementSmart(smartTarget, fallbackTarget);
    }

    if (targetElement) {
      // 生成唯一的选择器
      const uniqueSelector = generateUniqueSelector(targetElement);
      
      steps.push({
        ...stepProps,
        target: uniqueSelector,
      });
    } else {
      console.warn(`跳过步骤，未找到目标元素: ${smartTarget}`);
    }
  }

  return steps;
};

/**
 * 生成元素的唯一选择器
 * @param element 目标元素
 * @returns 唯一选择器字符串
 */
const generateUniqueSelector = (element: Element): string => {
  // 如果元素有唯一ID，直接使用
  if (element.id) {
    return `#${element.id}`;
  }

  // 构建路径选择器
  const path: string[] = [];
  let current: Element | null = element;

  while (current && current !== document.body) {
    let selector = current.tagName.toLowerCase();
    
    // 添加类名（选择最具特征性的类）
    if (current.className) {
      const classes = current.className.split(' ').filter(cls => 
        cls.startsWith('mantine-') || 
        cls.includes('card') || 
        cls.includes('button') ||
        cls.includes('input') ||
        cls.includes('tab')
      );
      if (classes.length > 0) {
        selector += '.' + classes[0];
      }
    }

    // 添加位置信息（如果有多个相同的兄弟元素）
    if (current.parentElement) {
      const siblings = Array.from(current.parentElement.children).filter(
        sibling => sibling.tagName === current!.tagName
      );
      if (siblings.length > 1) {
        const index = siblings.indexOf(current) + 1;
        selector += `:nth-of-type(${index})`;
      }
    }

    path.unshift(selector);
    current = current.parentElement;
  }

  return path.join(' > ');
};

/**
 * 根据页面获取智能引导步骤
 * @param page 页面类型
 * @returns Promise<Step[]> 标准步骤数组
 */
export const getSmartStepsByPage = async (page: string): Promise<Step[]> => {
  const smartSteps = smartTourSteps[page];
  if (!smartSteps) {
    return [];
  }

  return await convertSmartStepsToSteps(smartSteps);
};

/**
 * 验证页面元素是否准备就绪
 * @param page 页面类型
 * @returns Promise<boolean> 是否准备就绪
 */
export const validatePageReady = async (page: string): Promise<boolean> => {
  const smartSteps = smartTourSteps[page];
  if (!smartSteps || smartSteps.length === 0) {
    return true;
  }

  // 检查至少一个关键元素是否存在
  const firstStep = smartSteps[0];
  const element = await waitForElement(firstStep.smartTarget, 2000, firstStep.fallbackTarget);
  
  return element !== null;
};

// 导出智能步骤配置（用于调试）
export { smartTourSteps };
