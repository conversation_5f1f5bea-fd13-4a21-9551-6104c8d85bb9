import { Step } from 'react-joyride';

/**
 * 引导步骤配置
 * 使用智能选择器，支持多种元素匹配策略
 */

/**
 * 智能选择器配置
 * 基于组件结构和语义化选择器，无需手动添加data-tour属性
 */
export const tourSteps: { [key: string]: Step[] } = {

  // 对话页面引导
  chat: [
    {
      target: '.ql-editor, textarea[placeholder*="输入"], input[placeholder*="输入"], [role="textbox"]',
      content: '在这里输入您想要询问的问题。按 Shift+Enter 可以发送。',
      placement: 'top',
      disableBeacon: true,
    },
    {
      target: 'button[aria-label*="发送"], button[type="submit"]:has(svg), .mantine-Button-root:has([aria-label*="发送"])',
      content: '点击发送按钮或按 Enter 键发送消息。',
      placement: 'top',
    },
  ],

  // 智能体市场引导
  agent: [
    {
      target: '.mantine-Tabs-list, .filter-tabs, [role="tablist"]',
      content: '浏览不同类别的智能体，找到最适合您需求的AI助手。',
      placement: 'bottom',
      disableBeacon: true,
    },
    {
      target: '.mantine-UnstyledButton-root:has(.mantine-Image-root), .module-card, .agent-card',
      content: '点击智能体卡片查看详情或购买使用。',
      placement: 'top',
    },
  ],

  // 课程页面引导
  course: [
    {
      target: '.mantine-Tabs-list, .filter-tabs, [role="tablist"]',
      content: '选择您感兴趣的课程类别，系统学习相关知识。',
      placement: 'bottom',
      disableBeacon: true,
    },
    {
      target: '.mantine-UnstyledButton-root:has(.mantine-Image-root), .module-card, .course-card',
      content: '查看课程介绍和价格信息，选择适合的课程开始学习。',
      placement: 'top',
    },
  ],

  // 工作流页面引导
  workflow: [
    {
      target: '.mantine-Tabs-list, .filter-tabs, [role="tablist"]',
      content: '浏览不同类别的工作流，找到适合您业务需求的自动化流程。',
      placement: 'bottom',
      disableBeacon: true,
    },
    {
      target: '.mantine-UnstyledButton-root:has(.mantine-Image-root), .module-card, .workflow-card',
      content: '点击工作流卡片查看详情，了解自动化流程的功能和使用方法。',
      placement: 'top',
    },
  ],

  // 数字人页面引导
  digitalVideo: [
    {
      target: '.mantine-Tabs-list, .digital-filter, .avatar-categories',
      content: '选择不同风格的数字人，为您的内容创作增添个性化元素。',
      placement: 'bottom',
      disableBeacon: true,
    },
    {
      target: '.digital-card, .avatar-card, .mantine-Card-root',
      content: '预览数字人效果，选择最适合您需求的虚拟形象。',
      placement: 'top',
    },
  ],

  // 知识库页面引导
  knowledge: [
    {
      target: '.mantine-Card-root:has(.mantine-Text-root:contains("创建")), button:contains("创建"), .upload-button',
      content: '上传您的文档、资料，构建个人专属知识库。',
      placement: 'bottom',
      disableBeacon: true,
    },
    {
      target: '.mantine-SimpleGrid-root .mantine-Card-root:not(:first-child), .knowledge-list, .file-list',
      content: '管理您的知识库内容，支持搜索、编辑和删除操作。',
      placement: 'top',
    },
  ],

  // 个人中心引导
  ucenter: [
    {
      target: '.mantine-AppShell-section:has([href*="assets"]), nav a[href*="assets"]',
      content: '查看您购买的智能体和课程，管理您的数字资产。',
      placement: 'right',
      disableBeacon: true,
    },
    {
      target: '.mantine-AppShell-section:has([href*="usage"]), nav a[href*="usage"]',
      content: '查看您的使用情况和消费记录，了解服务使用详情。',
      placement: 'right',
    },
    {
      target: '.mantine-AppShell-section:has([href*="account"]), nav a[href*="account"]',
      content: '管理您的账号信息，包括个人资料和安全设置。',
      placement: 'right',
    },
  ],
};



// 根据页面获取引导步骤
export const getStepsByPage = (page: string): Step[] => {
  return tourSteps[page] || [];
};

// 根据路径确定页面类型的辅助函数
export const getPageTypeFromPath = (pathname: string): string => {
  if (pathname === '/chat') return 'chat';
  if (pathname.startsWith('/agent/marketplace')) return 'agent';
  if (pathname.startsWith('/workflow/marketplace')) return 'workflow';
  if (pathname.startsWith('/course')) return 'course';
  if (pathname.startsWith('/digitalVideo')) return 'digitalVideo';
  if (pathname.startsWith('/knowledge')) return 'knowledge';
  if (pathname.startsWith('/ucenter')) return 'ucenter';
  // 其他路径不启动引导
  return '';
};
