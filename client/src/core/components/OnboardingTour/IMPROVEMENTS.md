# 引导系统改进方案

## 问题分析

### 1. 引导内容不出现的原因
- **元素查找失败**: 智能选择器可能在页面渲染完成前执行
- **选择器不稳定**: 生成的选择器在DOM变化后失效
- **时机问题**: 页面元素异步加载导致查找失败

### 2. 引导高亮定位错误的原因
- **选择器失效**: 页面交互后DOM结构变化
- **状态不同步**: react-joyride的内部状态与实际DOM不匹配
- **缓存问题**: 生成的选择器被缓存，但对应元素已变化

## 解决方案

### 1. 稳定选择器系统 (StableTourSteps.ts)

**核心改进**:
- 使用多重备选选择器，提高匹配成功率
- 实时验证元素存在性和可见性
- 区分必需步骤和可选步骤

```typescript
// 示例：聊天输入框的稳定配置
{
  selectors: [
    '.ql-editor',                    // 主要选择器
    'textarea[placeholder*="输入"]',  // 备选1
    '[role="textbox"]',              // 备选2
    '.mantine-Textarea-input',       // 备选3
  ],
  validator: (el) => {
    const rect = el.getBoundingClientRect();
    return rect.height > 30 && rect.width > 200;
  },
  required: true, // 如果找不到则跳过整个引导
}
```

### 2. 实时验证机制

**改进点**:
- 每个步骤开始前验证目标元素
- 元素失效时自动重新生成步骤
- 支持步骤级别的错误恢复

```typescript
// 实时验证当前步骤
const validateCurrentStep = useCallback(() => {
  const step = validSteps[currentStepIndex];
  const element = document.querySelector(step.target);
  
  if (!element) {
    // 重新生成步骤
    validateAndUpdateSteps();
    return false;
  }
  
  return true;
}, [validSteps, currentStepIndex]);
```

### 3. 增强的回调处理

**改进点**:
- 更精确的步骤状态跟踪
- 错误自动恢复机制
- 防止意外点击导致的状态错乱

```typescript
const handleJoyrideCallback = useCallback((data) => {
  const { status, type, index } = data;
  
  // 步骤开始前验证
  if (type === 'step:before') {
    if (!validateCurrentStep()) {
      // 验证失败，重新开始
      setTimeout(() => validateAndUpdateSteps(), 500);
      return;
    }
  }
  
  // 错误恢复
  if (status === STATUS.ERROR) {
    setTimeout(() => validateAndUpdateSteps(), 500);
  }
}, [validateCurrentStep, validateAndUpdateSteps]);
```

## 主要改进

### 1. 稳定性提升
- ✅ 多重备选选择器策略
- ✅ 实时元素验证
- ✅ 自动错误恢复
- ✅ 必需/可选步骤区分

### 2. 用户体验改善
- ✅ 禁用意外点击高亮区域
- ✅ 更好的滚动行为
- ✅ 增强的视觉反馈
- ✅ 调试模式支持

### 3. 开发体验优化
- ✅ 更详细的调试信息
- ✅ 实时状态监控
- ✅ 错误日志和警告
- ✅ 配置验证工具

## 使用方式

### 1. 基本配置
```typescript
// 在 StableTourSteps.ts 中配置
chat: [
  {
    selectors: ['主选择器', '备选1', '备选2'],
    content: '引导内容',
    placement: 'top',
    required: true, // 必需步骤
    validator: (el) => /* 验证逻辑 */
  }
]
```

### 2. 调试工具
- 按 `Ctrl+Shift+D` 打开调试器
- 查看元素匹配状态
- 测试步骤生成结果
- 监控实时状态

### 3. 错误处理
- 自动重试机制
- 优雅降级
- 详细错误日志
- 开发模式调试

## 配置指南

### 1. 选择器优先级
1. **特征性类名**: `.ql-editor`, `.mantine-Button-root`
2. **语义化属性**: `[role="textbox"]`, `[aria-label*="发送"]`
3. **结构选择器**: `textarea[placeholder*="输入"]`
4. **通用备选**: `textarea`, `button`

### 2. 验证函数
```typescript
validator: (el) => {
  // 检查尺寸
  const rect = el.getBoundingClientRect();
  if (rect.width < 100 || rect.height < 30) return false;
  
  // 检查内容
  const text = el.textContent?.toLowerCase() || '';
  if (text.includes('创建')) return false;
  
  // 检查属性
  const href = el.getAttribute('href') || '';
  return href.includes('target-path');
}
```

### 3. 必需步骤标记
```typescript
{
  selectors: ['...'],
  required: true, // 找不到时跳过整个引导
  content: '关键功能说明'
}
```

## 故障排除

### 常见问题
1. **引导不启动**: 检查必需步骤是否找到
2. **步骤跳过**: 查看控制台警告信息
3. **定位错误**: 使用调试器检查选择器状态
4. **性能问题**: 减少验证函数复杂度

### 调试步骤
1. 打开调试器 (`Ctrl+Shift+D`)
2. 点击"收集调试信息"
3. 检查元素测试结果
4. 查看生成的步骤
5. 根据错误信息调整配置

## 最佳实践

1. **选择器设计**: 优先使用稳定的类名和属性
2. **验证函数**: 保持简单高效
3. **必需标记**: 谨慎使用，避免引导完全失效
4. **测试验证**: 在不同页面状态下测试
5. **错误监控**: 关注控制台警告和错误

这个改进方案解决了原有系统的主要问题，提供了更稳定、更可靠的引导体验。
