import { Step } from 'react-joyride';

/**
 * 稳定的引导步骤配置
 * 使用多重备选选择器确保稳定性
 */

interface StableStep extends Step {
  /** 多重选择器，按优先级排列 */
  selectors: string[];
  /** 元素验证函数 */
  validator?: (element: Element) => boolean;
  /** 是否必需（如果找不到则跳过整个引导） */
  required?: boolean;
}

// 稳定的引导步骤配置
export const stableTourSteps: { [key: string]: StableStep[] } = {
  // 对话页面引导
  chat: [
    {
      selectors: [
        '.ql-editor',
        'textarea[placeholder*="输入"]',
        'input[placeholder*="输入"]',
        '[role="textbox"]',
        '.mantine-Textarea-input',
        'textarea',
        'input[type="text"]'
      ],
      content: '在这里输入您想要询问的问题。按 Shift+Enter 可以发送。',
      placement: 'top',
      disableBeacon: true,
      target: '', // 将在运行时设置
      validator: (el) => {
        const rect = el.getBoundingClientRect();
        return rect.height > 30 && rect.width > 200;
      },
      required: true,
    },
    {
      selectors: [
        'button[aria-label*="发送"]',
        'button[type="submit"]:has(svg)',
        '.mantine-ActionIcon-root:last-child',
        'button:has(svg):last-of-type',
        '.mantine-Button-root:contains("发送")'
      ],
      content: '点击发送按钮或按 Enter 键发送消息。',
      placement: 'top',
      target: '', // 将在运行时设置
      validator: (el) => {
        const ariaLabel = el.getAttribute('aria-label')?.toLowerCase() || '';
        const hasIcon = el.querySelector('svg') !== null;
        return ariaLabel.includes('发送') || hasIcon;
      },
    },
  ],

  // 智能体市场引导
  agent: [
    {
      selectors: [
        '.mantine-Tabs-list',
        '.filter-tabs',
        '[role="tablist"]',
        '.mantine-SegmentedControl-root',
        '.category-filter'
      ],
      content: '浏览不同类别的智能体，找到最适合您需求的AI助手。',
      placement: 'bottom',
      disableBeacon: true,
      target: '',
      validator: (el) => {
        const tabs = el.querySelectorAll('[role="tab"], .mantine-Tabs-tab, .mantine-SegmentedControl-control');
        return tabs.length >= 2;
      },
      required: true,
    },
    {
      selectors: [
        '.mantine-UnstyledButton-root:has(.mantine-Image-root)',
        '.module-card',
        '.agent-card',
        '.mantine-Card-root:has(.mantine-Image-root)',
        '.mantine-Paper-root:has(img)'
      ],
      content: '点击智能体卡片查看详情或购买使用。',
      placement: 'top',
      target: '',
      validator: (el) => {
        const hasImage = el.querySelector('img, .mantine-Image-root') !== null;
        const hasText = el.textContent && el.textContent.trim().length > 0;
        return hasImage && hasText;
      },
    },
  ],

  // 课程页面引导
  course: [
    {
      selectors: [
        '.mantine-Tabs-list',
        '.filter-tabs',
        '[role="tablist"]',
        '.mantine-SegmentedControl-root'
      ],
      content: '选择您感兴趣的课程类别，系统学习相关知识。',
      placement: 'bottom',
      disableBeacon: true,
      target: '',
      validator: (el) => {
        const tabs = el.querySelectorAll('[role="tab"], .mantine-Tabs-tab');
        return tabs.length >= 2;
      },
      required: true,
    },
    {
      selectors: [
        '.mantine-UnstyledButton-root:has(.mantine-Image-root)',
        '.module-card',
        '.course-card',
        '.mantine-Card-root:has(.mantine-Image-root)'
      ],
      content: '查看课程介绍和价格信息，选择适合的课程开始学习。',
      placement: 'top',
      target: '',
      validator: (el) => {
        const hasImage = el.querySelector('img, .mantine-Image-root') !== null;
        return hasImage;
      },
    },
  ],

  // 工作流页面引导
  workflow: [
    {
      selectors: [
        '.mantine-Tabs-list',
        '.filter-tabs',
        '[role="tablist"]'
      ],
      content: '浏览不同类别的工作流，找到适合您业务需求的自动化流程。',
      placement: 'bottom',
      disableBeacon: true,
      target: '',
      required: true,
    },
    {
      selectors: [
        '.mantine-UnstyledButton-root:has(.mantine-Image-root)',
        '.module-card',
        '.workflow-card'
      ],
      content: '点击工作流卡片查看详情，了解自动化流程的功能和使用方法。',
      placement: 'top',
      target: '',
    },
  ],

  // 知识库页面引导
  knowledge: [
    {
      selectors: [
        '.mantine-Card-root:has(.mantine-Text-root:contains("创建"))',
        'button:contains("创建")',
        '.upload-button',
        '.mantine-Button-root:contains("创建")'
      ],
      content: '上传您的文档、资料，构建个人专属知识库。',
      placement: 'bottom',
      disableBeacon: true,
      target: '',
      validator: (el) => {
        const text = el.textContent?.toLowerCase() || '';
        return text.includes('创建') || text.includes('上传');
      },
      required: true,
    },
    {
      selectors: [
        '.mantine-SimpleGrid-root .mantine-Card-root:not(:first-child)',
        '.knowledge-list .mantine-Card-root',
        '.mantine-Card-root:has(.mantine-ActionIcon-root)'
      ],
      content: '管理您的知识库内容，支持搜索、编辑和删除操作。',
      placement: 'top',
      target: '',
      validator: (el) => {
        // 确保不是创建卡片
        const text = el.textContent?.toLowerCase() || '';
        return !text.includes('创建') && text.length > 10;
      },
    },
  ],

  // 个人中心引导
  ucenter: [
    {
      selectors: [
        'nav a[href*="assets"]',
        '.mantine-AppShell-section a[href*="assets"]',
        '.mantine-UnstyledButton-root:has(.mantine-Text-root:contains("资产"))'
      ],
      content: '查看您购买的智能体和课程，管理您的数字资产。',
      placement: 'right',
      disableBeacon: true,
      target: '',
      validator: (el) => {
        const text = el.textContent?.toLowerCase() || '';
        const href = el.getAttribute('href') || '';
        return text.includes('资产') || href.includes('assets');
      },
      required: true,
    },
    {
      selectors: [
        'nav a[href*="usage"]',
        '.mantine-AppShell-section a[href*="usage"]',
        '.mantine-UnstyledButton-root:has(.mantine-Text-root:contains("用量"))'
      ],
      content: '查看您的使用情况和消费记录，了解服务使用详情。',
      placement: 'right',
      target: '',
      validator: (el) => {
        const text = el.textContent?.toLowerCase() || '';
        const href = el.getAttribute('href') || '';
        return text.includes('用量') || href.includes('usage');
      },
    },
    {
      selectors: [
        'nav a[href*="account"]',
        '.mantine-AppShell-section a[href*="account"]',
        '.mantine-UnstyledButton-root:has(.mantine-Text-root:contains("账号"))'
      ],
      content: '管理您的账号信息，包括个人资料和安全设置。',
      placement: 'right',
      target: '',
      validator: (el) => {
        const text = el.textContent?.toLowerCase() || '';
        const href = el.getAttribute('href') || '';
        return text.includes('账号') || href.includes('account');
      },
    },
  ],
};

/**
 * 查找稳定的元素
 * @param selectors 选择器数组
 * @param validator 验证函数
 * @returns 找到的元素或null
 */
export const findStableElement = (
  selectors: string[], 
  validator?: (element: Element) => boolean
): Element | null => {
  for (const selector of selectors) {
    try {
      const elements = document.querySelectorAll(selector);
      
      for (const element of Array.from(elements)) {
        // 检查元素是否可见
        const rect = element.getBoundingClientRect();
        if (rect.width === 0 || rect.height === 0) continue;
        
        // 检查元素是否在合理位置
        if (rect.bottom < -50 || rect.top > window.innerHeight + 50) continue;
        
        // 应用验证函数
        if (validator && !validator(element)) continue;
        
        return element;
      }
    } catch (error) {
      console.warn(`选择器执行失败: ${selector}`, error);
      continue;
    }
  }
  
  return null;
};

/**
 * 生成稳定的选择器
 * @param element 目标元素
 * @returns 稳定的选择器字符串
 */
export const generateStableSelector = (element: Element): string => {
  // 优先使用ID
  if (element.id) {
    return `#${element.id}`;
  }

  // 使用特征性的类名
  const classList = Array.from(element.classList);
  const stableClasses = classList.filter(cls => 
    cls.startsWith('mantine-') || 
    cls.includes('card') || 
    cls.includes('button') ||
    cls.includes('input') ||
    cls.includes('tab') ||
    cls.includes('ql-')
  );

  if (stableClasses.length > 0) {
    const selector = element.tagName.toLowerCase() + '.' + stableClasses[0];
    // 验证选择器的唯一性
    const matches = document.querySelectorAll(selector);
    if (matches.length === 1) {
      return selector;
    }
  }

  // 使用属性选择器
  const attributes = ['role', 'aria-label', 'placeholder', 'type'];
  for (const attr of attributes) {
    const value = element.getAttribute(attr);
    if (value) {
      const selector = `${element.tagName.toLowerCase()}[${attr}="${value}"]`;
      const matches = document.querySelectorAll(selector);
      if (matches.length === 1) {
        return selector;
      }
    }
  }

  // 最后使用位置选择器
  let current: Element | null = element;
  const path: string[] = [];
  
  while (current && current !== document.body && path.length < 5) {
    let selector = current.tagName.toLowerCase();
    
    if (current.parentElement) {
      const siblings = Array.from(current.parentElement.children).filter(
        sibling => sibling.tagName === current!.tagName
      );
      if (siblings.length > 1) {
        const index = siblings.indexOf(current) + 1;
        selector += `:nth-of-type(${index})`;
      }
    }
    
    path.unshift(selector);
    current = current.parentElement;
  }

  return path.join(' > ');
};

/**
 * 转换为标准步骤
 * @param page 页面类型
 * @returns 标准步骤数组
 */
export const convertToStandardSteps = (page: string): Step[] => {
  const stableSteps = stableTourSteps[page];
  if (!stableSteps) {
    return [];
  }

  const steps: Step[] = [];

  for (const stableStep of stableSteps) {
    const { selectors, validator, required, ...stepProps } = stableStep;
    
    const element = findStableElement(selectors, validator);
    
    if (element) {
      const stableSelector = generateStableSelector(element);
      steps.push({
        ...stepProps,
        target: stableSelector,
      });
    } else if (required) {
      console.warn(`必需的引导步骤元素未找到，跳过整个引导: ${page}`);
      return [];
    } else {
      console.warn(`可选的引导步骤元素未找到，跳过该步骤`);
    }
  }

  return steps;
};
