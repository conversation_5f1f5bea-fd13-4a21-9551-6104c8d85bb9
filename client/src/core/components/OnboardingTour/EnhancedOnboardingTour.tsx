import React, { useEffect, useState } from 'react';
import Joyride, { CallBackProps, STATUS, Step } from 'react-joyride';
import { useLocation } from 'react-router-dom';
import { useOnboardingStore } from './useOnboardingStore';
import { getStepsByPage, getPageTypeFromPath } from './tourSteps';

interface EnhancedOnboardingTourProps {
  page?: string;
}

/**
 * 增强的引导组件
 * 支持智能元素查找和动态步骤调整
 */
const EnhancedOnboardingTour: React.FC<EnhancedOnboardingTourProps> = ({ page }) => {
  const location = useLocation();
  const { isRunning, stopTour, markPageCompleted, isPageCompleted } = useOnboardingStore();
  const [validSteps, setValidSteps] = useState<Step[]>([]);

  // 确定当前页面
  const currentPage = page || getPageTypeFromPath(location.pathname);

  // 验证步骤中的元素是否存在
  const validateSteps = (steps: Step[]): Step[] => {
    return steps.filter(step => {
      try {
        const element = document.querySelector(step.target as string);
        if (!element) {
          console.warn(`引导目标元素未找到: ${step.target}`);
          return false;
        }
        return true;
      } catch (error) {
        console.warn(`无效的选择器: ${step.target}`, error);
        return false;
      }
    });
  };

  // 获取并验证当前页面的引导步骤
  useEffect(() => {
    const steps = getStepsByPage(currentPage);
    
    // 延迟验证，确保页面元素已加载
    const timer = setTimeout(() => {
      const valid = validateSteps(steps);
      setValidSteps(valid);
      
      if (valid.length === 0 && steps.length > 0) {
        console.warn(`页面 ${currentPage} 的所有引导目标元素都未找到`);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [currentPage, location.pathname]);

  // 检查是否需要显示引导
  const shouldShowTour = isRunning && validSteps.length > 0 && !isPageCompleted(currentPage);

  // 处理引导回调
  const handleJoyrideCallback = (data: CallBackProps) => {
    const { status, action, index, type } = data;

    // 如果当前步骤的目标元素不存在，跳过该步骤
    if (type === 'step:before' && validSteps[index]) {
      const element = document.querySelector(validSteps[index].target as string);
      if (!element) {
        console.warn(`步骤 ${index} 的目标元素不存在，跳过该步骤`);
        return;
      }
    }

    if (status === STATUS.FINISHED || status === STATUS.SKIPPED) {
      markPageCompleted(currentPage);
      stopTour();
    }
  };

  // 如果没有有效步骤或不需要显示引导，返回null
  if (!shouldShowTour) {
    return null;
  }

  return (
    <Joyride
      steps={validSteps}
      run={true}
      continuous={true}
      showSkipButton={true}
      callback={handleJoyrideCallback}
      styles={{
        options: {
          primaryColor: '#228be6',
          zIndex: 10000,
        },
        tooltip: {
          borderRadius: 8,
          fontSize: 14,
        },
        tooltipContainer: {
          textAlign: 'left',
        },
        buttonNext: {
          backgroundColor: '#228be6',
          borderRadius: 6,
          fontSize: 14,
          padding: '8px 16px',
        },
        buttonBack: {
          color: '#666',
          marginRight: 10,
          fontSize: 14,
        },
        buttonSkip: {
          color: '#999',
          fontSize: 14,
        },
      }}
      locale={{
        back: '上一步',
        close: '关闭',
        last: '完成',
        next: '下一步',
        skip: '跳过',
      }}
      // 增强配置
      disableOverlayClose={false}
      disableScrolling={false}
      hideCloseButton={false}
      spotlightClicks={true}
      spotlightPadding={4}
    />
  );
};

export default EnhancedOnboardingTour;
