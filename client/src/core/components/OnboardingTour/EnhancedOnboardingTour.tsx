import React, { useEffect, useState, useRef, useCallback } from 'react';
import Joyride, { CallBackProps, STATUS, Step } from 'react-joyride';
import { useLocation } from 'react-router-dom';
import { useOnboardingStore } from './useOnboardingStore';
import { getPageTypeFromPath } from './tourSteps';
import { findElementSmart } from './SmartSelector';

interface EnhancedOnboardingTourProps {
  page?: string;
}

/**
 * 增强的引导组件
 * 支持稳定的元素查找和实时验证
 */
const EnhancedOnboardingTour: React.FC<EnhancedOnboardingTourProps> = ({ page }) => {
  const location = useLocation();
  const { isRunning, stopTour, markPageCompleted, isPageCompleted } = useOnboardingStore();
  const [validSteps, setValidSteps] = useState<Step[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const joyrideRef = useRef<any>(null);

  // 确定当前页面
  const currentPage = page || getPageTypeFromPath(location.pathname);

  // 动态验证和更新步骤
  const validateAndUpdateSteps = useCallback(() => {
    if (!currentPage) {
      setValidSteps([]);
      return;
    }

    // 导入稳定步骤转换函数
    import('./StableTourSteps').then(({ convertToStandardSteps }) => {
      try {
        const steps = convertToStandardSteps(currentPage);
        setValidSteps(steps);

        if (steps.length === 0) {
          console.warn(`页面 ${currentPage} 没有可用的引导步骤`);
        } else {
          console.log(`页面 ${currentPage} 加载了 ${steps.length} 个引导步骤`);
        }
      } catch (error) {
        console.error(`加载页面 ${currentPage} 的引导步骤失败:`, error);
        setValidSteps([]);
      }
    });
  }, [currentPage]);

  // 页面变化时重新加载步骤
  useEffect(() => {
    setIsLoading(true);

    // 延迟加载，确保页面元素已渲染
    const timer = setTimeout(() => {
      validateAndUpdateSteps();
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [currentPage, location.pathname, validateAndUpdateSteps]);

  // 实时验证当前步骤的元素
  const validateCurrentStep = useCallback(() => {
    if (!validSteps[currentStepIndex]) return true;

    const step = validSteps[currentStepIndex];
    const element = document.querySelector(step.target as string);

    if (!element) {
      console.warn(`当前步骤的目标元素不存在: ${step.target}`);
      // 尝试重新生成步骤
      validateAndUpdateSteps();
      return false;
    }

    // 检查元素是否可见
    const rect = element.getBoundingClientRect();
    if (rect.width === 0 || rect.height === 0) {
      console.warn(`当前步骤的目标元素不可见`);
      return false;
    }

    return true;
  }, [validSteps, currentStepIndex, validateAndUpdateSteps]);

  // 检查是否需要显示引导
  const shouldShowTour = isRunning && validSteps.length > 0 && !isPageCompleted(currentPage) && !isLoading;

  // 处理引导回调
  const handleJoyrideCallback = useCallback((data: CallBackProps) => {
    const { status, action, index, type } = data;

    // 更新当前步骤索引
    if (type === 'step:after') {
      setCurrentStepIndex(index);
    }

    // 步骤开始前的验证
    if (type === 'step:before') {
      // 验证当前步骤
      if (!validateCurrentStep()) {
        // 如果验证失败，停止引导并重新开始
        console.warn(`步骤 ${index} 验证失败，重新开始引导`);
        setTimeout(() => {
          validateAndUpdateSteps();
        }, 500);
        return;
      }
    }

    // 处理引导完成或跳过
    if (status === STATUS.FINISHED || status === STATUS.SKIPPED) {
      markPageCompleted(currentPage);
      stopTour();
      setCurrentStepIndex(0);
    }

    // 处理引导错误
    if (status === STATUS.ERROR) {
      console.error('引导出现错误，重新开始');
      setTimeout(() => {
        validateAndUpdateSteps();
      }, 500);
    }
  }, [currentPage, markPageCompleted, stopTour, validateCurrentStep, validateAndUpdateSteps]);

  // 监听页面变化，重置引导状态
  useEffect(() => {
    setCurrentStepIndex(0);
  }, [currentPage]);

  // 如果正在加载、没有有效步骤或不需要显示引导，返回null
  if (!shouldShowTour) {
    return null;
  }

  return (
    <Joyride
      ref={joyrideRef}
      steps={validSteps}
      run={true}
      continuous={true}
      showSkipButton={true}
      callback={handleJoyrideCallback}
      stepIndex={currentStepIndex}
      styles={{
        options: {
          primaryColor: '#228be6',
          zIndex: 10000,
        },
        tooltip: {
          borderRadius: 8,
          fontSize: 14,
          maxWidth: 400,
        },
        tooltipContainer: {
          textAlign: 'left',
        },
        buttonNext: {
          backgroundColor: '#228be6',
          borderRadius: 6,
          fontSize: 14,
          padding: '8px 16px',
        },
        buttonBack: {
          color: '#666',
          marginRight: 10,
          fontSize: 14,
        },
        buttonSkip: {
          color: '#999',
          fontSize: 14,
        },
        spotlight: {
          borderRadius: 4,
        },
      }}
      locale={{
        back: '上一步',
        close: '关闭',
        last: '完成',
        next: '下一步',
        skip: '跳过',
      }}
      // 增强配置
      disableOverlayClose={false}
      disableScrolling={false}
      hideCloseButton={false}
      spotlightClicks={false} // 禁用点击高亮区域，避免意外触发
      spotlightPadding={8}
      scrollToFirstStep={true}
      scrollOffset={100}
      // 添加错误恢复机制
      debug={process.env.NODE_ENV === 'development'}
    />
  );
};

export default EnhancedOnboardingTour;
