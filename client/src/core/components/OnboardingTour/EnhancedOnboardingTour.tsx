import React, { useEffect, useState } from 'react';
import Joyride, { CallBackProps, STATUS, Step } from 'react-joyride';
import { useLocation } from 'react-router-dom';
import { useOnboardingStore } from './useOnboardingStore';
import { getPageTypeFromPath } from './tourSteps';
import { getSmartStepsByPage, validatePageReady } from './SmartTourSteps';

interface EnhancedOnboardingTourProps {
  page?: string;
}

/**
 * 增强的引导组件
 * 支持智能元素查找和动态步骤调整
 */
const EnhancedOnboardingTour: React.FC<EnhancedOnboardingTourProps> = ({ page }) => {
  const location = useLocation();
  const { isRunning, stopTour, markPageCompleted, isPageCompleted } = useOnboardingStore();
  const [validSteps, setValidSteps] = useState<Step[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // 确定当前页面
  const currentPage = page || getPageTypeFromPath(location.pathname);

  // 获取并验证当前页面的智能引导步骤
  useEffect(() => {
    const loadSmartSteps = async () => {
      if (!currentPage) {
        setValidSteps([]);
        return;
      }

      setIsLoading(true);

      try {
        // 首先验证页面是否准备就绪
        const isReady = await validatePageReady(currentPage);
        if (!isReady) {
          console.warn(`页面 ${currentPage} 尚未准备就绪，跳过引导`);
          setValidSteps([]);
          return;
        }

        // 获取智能步骤
        const steps = await getSmartStepsByPage(currentPage);
        setValidSteps(steps);

        if (steps.length === 0) {
          console.warn(`页面 ${currentPage} 没有可用的引导步骤`);
        } else {
          console.log(`页面 ${currentPage} 加载了 ${steps.length} 个引导步骤`);
        }
      } catch (error) {
        console.error(`加载页面 ${currentPage} 的引导步骤失败:`, error);
        setValidSteps([]);
      } finally {
        setIsLoading(false);
      }
    };

    // 延迟加载，确保页面元素已渲染
    const timer = setTimeout(loadSmartSteps, 800);
    return () => clearTimeout(timer);
  }, [currentPage, location.pathname]);

  // 检查是否需要显示引导
  const shouldShowTour = isRunning && validSteps.length > 0 && !isPageCompleted(currentPage) && !isLoading;

  // 处理引导回调
  const handleJoyrideCallback = (data: CallBackProps) => {
    const { status, action, index, type } = data;

    // 增强的步骤验证
    if (type === 'step:before' && validSteps[index]) {
      const step = validSteps[index];
      const element = document.querySelector(step.target as string);

      if (!element) {
        console.warn(`步骤 ${index} 的目标元素不存在: ${step.target}`);
        return;
      }

      // 检查元素是否可见
      const rect = element.getBoundingClientRect();
      if (rect.width === 0 || rect.height === 0) {
        console.warn(`步骤 ${index} 的目标元素不可见`);
        return;
      }
    }

    if (status === STATUS.FINISHED || status === STATUS.SKIPPED) {
      markPageCompleted(currentPage);
      stopTour();
    }
  };

  // 如果正在加载、没有有效步骤或不需要显示引导，返回null
  if (!shouldShowTour) {
    return null;
  }

  return (
    <Joyride
      steps={validSteps}
      run={true}
      continuous={true}
      showSkipButton={true}
      callback={handleJoyrideCallback}
      styles={{
        options: {
          primaryColor: '#228be6',
          zIndex: 10000,
        },
        tooltip: {
          borderRadius: 8,
          fontSize: 14,
        },
        tooltipContainer: {
          textAlign: 'left',
        },
        buttonNext: {
          backgroundColor: '#228be6',
          borderRadius: 6,
          fontSize: 14,
          padding: '8px 16px',
        },
        buttonBack: {
          color: '#666',
          marginRight: 10,
          fontSize: 14,
        },
        buttonSkip: {
          color: '#999',
          fontSize: 14,
        },
      }}
      locale={{
        back: '上一步',
        close: '关闭',
        last: '完成',
        next: '下一步',
        skip: '跳过',
      }}
      // 增强配置
      disableOverlayClose={false}
      disableScrolling={false}
      hideCloseButton={false}
      spotlightClicks={true}
      spotlightPadding={4}
    />
  );
};

export default EnhancedOnboardingTour;
