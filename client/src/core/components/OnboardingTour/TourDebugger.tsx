import React, { useState, useEffect } from 'react';
import { But<PERSON>, Stack, Text, Code, Group, Badge, Divider } from '@mantine/core';
import { useLocation } from 'react-router-dom';
import { getPageTypeFromPath } from './tourSteps';
import { getValidSteps, simpleTourSteps, isElementValid, waitForPageReady } from './SimpleTourSteps';
import { useOnboardingStore } from './useOnboardingStore';

/**
 * 引导系统调试器
 * 用于测试和调试智能选择器系统
 */
const TourDebugger: React.FC = () => {
  const location = useLocation();
  const { startTour, stopTour, isRunning, resetPageCompleted } = useOnboardingStore();
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [isVisible, setIsVisible] = useState(false);

  const currentPage = getPageTypeFromPath(location.pathname);

  // 调试信息收集
  const collectDebugInfo = async () => {
    const info: any = {
      currentPage,
      pathname: location.pathname,
      isRunning,
      simpleStepsConfig: simpleTourSteps[currentPage] || [],
      elementTests: {},
      generatedSteps: [],
      pageReady: false,
    };

    // 测试页面准备状态
    try {
      info.pageReady = await waitForPageReady(currentPage);
    } catch (error) {
      info.pageReadyError = error;
    }

    // 测试简单选择器
    const simpleSteps = simpleTourSteps[currentPage] || [];
    for (let i = 0; i < simpleSteps.length; i++) {
      const step = simpleSteps[i];
      try {
        const isValid = isElementValid(step.target as string);
        const element = document.querySelector(step.target as string);
        info.elementTests[`step-${i}`] = {
          found: isValid,
          target: step.target,
          element: element ? generateElementInfo(element) : null,
          content: step.content,
        };
      } catch (error) {
        info.elementTests[`step-${i}`] = {
          found: false,
          target: step.target,
          error: error.message,
          content: step.content,
        };
      }
    }

    // 生成步骤
    try {
      info.generatedSteps = getValidSteps(currentPage);
    } catch (error) {
      info.generatedStepsError = error;
    }

    setDebugInfo(info);
  };

  // 生成元素信息
  const generateElementInfo = (element: Element) => {
    const rect = element.getBoundingClientRect();
    return {
      tagName: element.tagName,
      className: element.className,
      id: element.id,
      textContent: element.textContent?.substring(0, 50) + '...',
      rect: {
        width: Math.round(rect.width),
        height: Math.round(rect.height),
        top: Math.round(rect.top),
        left: Math.round(rect.left),
      },
      visible: rect.width > 0 && rect.height > 0,
    };
  };

  // 手动启动引导
  const handleStartTour = () => {
    resetPageCompleted(currentPage);
    startTour();
  };

  // 切换调试器可见性
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'D') {
        setIsVisible(!isVisible);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isVisible]);

  if (!isVisible) {
    return (
      <div
        style={{
          position: 'fixed',
          bottom: 20,
          right: 20,
          zIndex: 9999,
        }}
      >
        <Button
          size="xs"
          variant="outline"
          onClick={() => setIsVisible(true)}
        >
          调试引导
        </Button>
      </div>
    );
  }

  return (
    <div
      style={{
        position: 'fixed',
        top: 20,
        right: 20,
        width: 400,
        maxHeight: '80vh',
        overflow: 'auto',
        background: 'white',
        border: '1px solid #ccc',
        borderRadius: 8,
        padding: 16,
        zIndex: 9999,
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      }}
    >
      <Group justify="space-between" mb="md">
        <Text fw={600}>引导系统调试器</Text>
        <Button size="xs" variant="subtle" onClick={() => setIsVisible(false)}>
          ✕
        </Button>
      </Group>

      <Stack gap="sm">
        <Group>
          <Badge color={currentPage ? 'blue' : 'gray'}>
            页面: {currentPage || '未识别'}
          </Badge>
          <Badge color={isRunning ? 'green' : 'gray'}>
            {isRunning ? '运行中' : '已停止'}
          </Badge>
        </Group>

        <Group>
          <Button size="xs" onClick={collectDebugInfo}>
            收集调试信息
          </Button>
          <Button size="xs" onClick={handleStartTour} disabled={!currentPage}>
            启动引导
          </Button>
          <Button size="xs" onClick={stopTour} disabled={!isRunning}>
            停止引导
          </Button>
        </Group>

        <Divider />

        {debugInfo && (
          <Stack gap="xs">
            <Text size="sm" fw={500}>调试信息:</Text>
            
            <Text size="xs">
              页面准备状态: {' '}
              <Badge color={debugInfo.pageReady ? 'green' : 'red'} size="xs">
                {debugInfo.pageReady ? '就绪' : '未就绪'}
              </Badge>
            </Text>

            <Text size="xs">
              配置步骤数: {debugInfo.simpleStepsConfig.length}
            </Text>

            <Text size="xs">
              生成步骤数: {debugInfo.generatedSteps.length}
            </Text>

            <Divider />

            <Text size="sm" fw={500}>元素测试结果:</Text>
            <Stack gap={4}>
              {Object.entries(debugInfo.elementTests).map(([key, test]: [string, any]) => (
                <div key={key}>
                  <Group justify="space-between">
                    <Text size="xs">{key}</Text>
                    <Badge
                      color={test.found ? 'green' : 'red'}
                      size="xs"
                    >
                      {test.found ? '找到' : '未找到'}
                    </Badge>
                  </Group>
                  <Text size="xs" c="dimmed" style={{ marginLeft: 8 }}>
                    目标: {test.target}
                  </Text>
                  <Text size="xs" c="dimmed" style={{ marginLeft: 8 }}>
                    内容: {test.content?.substring(0, 30)}...
                  </Text>
                </div>
              ))}
            </Stack>

            {debugInfo.generatedSteps.length > 0 && (
              <>
                <Divider />
                <Text size="sm" fw={500}>生成的步骤:</Text>
                <Stack gap={4}>
                  {debugInfo.generatedSteps.map((step: any, index: number) => (
                    <div key={index}>
                      <Text size="xs" fw={500}>步骤 {index + 1}:</Text>
                      <Code block size="xs" style={{ fontSize: '10px' }}>
                        {step.target}
                      </Code>
                      <Text size="xs" c="dimmed">
                        {step.content.substring(0, 50)}...
                      </Text>
                    </div>
                  ))}
                </Stack>
              </>
            )}
          </Stack>
        )}

        <Text size="xs" c="dimmed" mt="md">
          按 Ctrl+Shift+D 切换显示
        </Text>
      </Stack>
    </div>
  );
};

export default TourDebugger;
