import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useOnboardingStore } from './useOnboardingStore';
import { getPageTypeFromPath } from './tourSteps';

/**
 * 简化的引导Hook
 * 自动处理页面引导逻辑，无需手动管理
 */
export const useSimpleTour = (options?: {
  /** 自定义页面类型 */
  pageType?: string;
  /** 是否禁用自动引导 */
  disabled?: boolean;
  /** 延迟时间（毫秒） */
  delay?: number;
}) => {
  const location = useLocation();
  const {
    startTour,
    isPageCompleted,
    isRunning,
    stopTour,
    markPageCompleted,
    resetPageCompleted
  } = useOnboardingStore();
  
  const {
    pageType: customPageType,
    disabled = false,
    delay = 1000
  } = options || {};

  // 确定当前页面类型
  const currentPageType = customPageType || getPageTypeFromPath(location.pathname);

  // 自动启动引导
  useEffect(() => {
    if (disabled) return;
    
    // 如果页面未完成引导，则启动引导
    if (!isPageCompleted(currentPageType)) {
      const timer = setTimeout(() => {
        startTour();
      }, delay);
      
      return () => clearTimeout(timer);
    }
  }, [currentPageType, isPageCompleted, startTour, disabled, delay]);

  // 手动控制函数
  const manualControls = {
    /** 手动启动当前页面引导 */
    start: () => startTour(),
    
    /** 停止引导 */
    stop: () => stopTour(),
    
    /** 标记当前页面引导完成 */
    complete: () => markPageCompleted(currentPageType),
    
    /** 重置当前页面引导状态 */
    reset: () => resetPageCompleted(currentPageType),
    
    /** 检查当前页面是否已完成引导 */
    isCompleted: () => isPageCompleted(currentPageType),
    
    /** 当前是否正在运行引导 */
    isRunning: () => isRunning,
    
    /** 当前页面类型 */
    currentPage: currentPageType,
  };

  return manualControls;
};

/**
 * 页面级引导Hook
 * 用于特定页面的引导控制
 */
export const usePageTour = (pageType: string, options?: {
  /** 是否自动启动 */
  autoStart?: boolean;
  /** 延迟时间 */
  delay?: number;
}) => {
  return useSimpleTour({
    pageType,
    disabled: !options?.autoStart,
    delay: options?.delay,
  });
};

/**
 * 条件引导Hook
 * 基于条件决定是否启动引导
 */
export const useConditionalTour = (
  condition: boolean,
  pageType?: string,
  delay: number = 1000
) => {
  return useSimpleTour({
    pageType,
    disabled: !condition,
    delay,
  });
};
