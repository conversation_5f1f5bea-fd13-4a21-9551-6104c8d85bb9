import React, { useEffect, useState, useCallback } from 'react';
import Joyride, { CallBackProps, STATUS, Step } from 'react-joyride';
import { useLocation } from 'react-router-dom';
import { useOnboardingStore } from './useOnboardingStore';
import { getPageTypeFromPath } from './tourSteps';
import { getValidSteps, waitForPageReady } from './SimpleTourSteps';

interface SimpleTourProps {
  page?: string;
}

/**
 * 简化的引导组件
 * 使用基本选择器，确保稳定性和可靠性
 */
const SimpleTour: React.FC<SimpleTourProps> = ({ page }) => {
  const location = useLocation();
  const { isRunning, stopTour, markPageCompleted, isPageCompleted } = useOnboardingStore();
  const [steps, setSteps] = useState<Step[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // 确定当前页面
  const currentPage = page || getPageTypeFromPath(location.pathname);

  // 加载引导步骤
  const loadSteps = useCallback(async () => {
    if (!currentPage) {
      setSteps([]);
      return;
    }

    setIsLoading(true);
    console.log(`开始加载页面 ${currentPage} 的引导步骤`);

    try {
      // 等待页面准备就绪
      const isReady = await waitForPageReady(currentPage);
      if (!isReady) {
        console.warn(`页面 ${currentPage} 未准备就绪，跳过引导`);
        setSteps([]);
        return;
      }

      // 获取有效步骤
      const validSteps = getValidSteps(currentPage);
      setSteps(validSteps);

      if (validSteps.length === 0) {
        console.warn(`页面 ${currentPage} 没有有效的引导步骤`);
      } else {
        console.log(`页面 ${currentPage} 成功加载 ${validSteps.length} 个引导步骤`);
      }
    } catch (error) {
      console.error(`加载页面 ${currentPage} 的引导步骤失败:`, error);
      setSteps([]);
    } finally {
      setIsLoading(false);
    }
  }, [currentPage]);

  // 页面变化时重新加载步骤
  useEffect(() => {
    // 延迟加载，确保页面元素已渲染
    const timer = setTimeout(loadSteps, 1500);
    return () => clearTimeout(timer);
  }, [currentPage, location.pathname, loadSteps]);

  // 处理引导回调
  const handleJoyrideCallback = useCallback((data: CallBackProps) => {
    const { status, action, index, type } = data;

    console.log('引导回调:', { status, action, index, type, currentPage });

    // 处理引导完成或跳过
    if (status === STATUS.FINISHED || status === STATUS.SKIPPED) {
      console.log(`引导完成: ${currentPage}`);
      markPageCompleted(currentPage);
      stopTour();
    }

    // 处理引导错误
    if (status === STATUS.ERROR) {
      console.error('引导出现错误:', data);
      // 不自动重新开始，避免无限循环
    }
  }, [currentPage, markPageCompleted, stopTour]);

  // 检查是否需要显示引导
  const shouldShowTour = isRunning && 
                        steps.length > 0 && 
                        !isPageCompleted(currentPage) && 
                        !isLoading &&
                        currentPage; // 确保有页面类型

  console.log('引导状态:', {
    isRunning,
    stepsLength: steps.length,
    isPageCompleted: isPageCompleted(currentPage),
    isLoading,
    currentPage,
    shouldShowTour
  });

  // 如果不需要显示引导，返回null
  if (!shouldShowTour) {
    return null;
  }

  return (
    <Joyride
      steps={steps}
      run={true}
      continuous={true}
      showSkipButton={true}
      callback={handleJoyrideCallback}
      styles={{
        options: {
          primaryColor: '#228be6',
          zIndex: 10000,
        },
        tooltip: {
          borderRadius: 8,
          fontSize: 14,
          maxWidth: 400,
        },
        tooltipContainer: {
          textAlign: 'left',
        },
        buttonNext: {
          backgroundColor: '#228be6',
          borderRadius: 6,
          fontSize: 14,
          padding: '8px 16px',
        },
        buttonBack: {
          color: '#666',
          marginRight: 10,
          fontSize: 14,
        },
        buttonSkip: {
          color: '#999',
          fontSize: 14,
        },
        spotlight: {
          borderRadius: 4,
        },
      }}
      locale={{
        back: '上一步',
        close: '关闭',
        last: '完成',
        next: '下一步',
        skip: '跳过',
      }}
      // 基本配置
      disableOverlayClose={false}
      disableScrolling={false}
      hideCloseButton={false}
      spotlightClicks={false}
      spotlightPadding={8}
      scrollToFirstStep={true}
      scrollOffset={100}
      // 调试模式
      debug={process.env.NODE_ENV === 'development'}
    />
  );
};

export default SimpleTour;
