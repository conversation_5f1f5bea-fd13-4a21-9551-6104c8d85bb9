export interface MenuItem {
  /** 显示文本 */
  label: string;

  /** 图标 */
  icon: React.ComponentType<{ size?: number }>;

  /** 跳转路径 */
  path?: string;

  /** 点击事件 */
  onClick?: () => void;

  /** 子菜单项 */
  children?: MenuItem[];

  /** 引导教程ID */
  tourId?: string;
}

export interface MenuProps {
  /**
   * Array of menu items to display
   */
  items: MenuItem[];

  /** 是否展示箭头 */
  showArrow?: boolean;

  onClick?: (item: MenuItem) => void;
}
