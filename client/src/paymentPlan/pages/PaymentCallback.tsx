import { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Container, Paper, Title, Text, Loader, Alert } from '@mantine/core';
import { API_BASE_URL } from '~/core/constants';

// 定义验证结果的类型
interface VerificationResult {
  data?: {
    verification_success: boolean;
    order_id?: string;
  };
  error?: {
    message: string;
    type?: string;
  };
}

/**
 * 支付回调页面
 * 根据 payment-notification-flow.puml：
 * 1. 接收支付网关的跳转
 * 2. 将支付参数转发给API验证
 * 3. 根据API返回结果展示成功或失败页面
 */
export default function PaymentCallback() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [verificationResult, setVerificationResult] = useState<VerificationResult | null>(null);

  useEffect(() => {
    const verifyPayment = async () => {
      try {
        // 获取所有URL参数
        const params: Record<string, string> = {};
        searchParams.forEach((value, key) => {
          params[key] = value;
        });

        console.log('收到支付宝回调参数:', params);

        // 检查是否有必要的支付宝参数
        if (!params.out_trade_no || !params.sign) {
          throw new Error('缺少必要的支付宝参数');
        }

        // 调用支付宝验证接口
        const response = await fetch(`${API_BASE_URL}/orders/alipay/verify`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            payment_params: params
          }),
        });

        const result = await response.json();
        console.log('API验证结果:', result);

        setVerificationResult(result);

        if (result.data?.verification_success) {
          // 通知父窗口支付成功（如果是在弹窗中打开）
          try {
            if (window.opener) {
              window.opener.postMessage({
                type: 'PAYMENT_SUCCESS',
                orderId: result.data.order_id,
                status: 'success',
                data: result.data
              }, '*');
            }
          } catch (e) {
            console.log('无法通知父窗口:', e);
          }

          // 延迟跳转到成功页面
          setTimeout(() => {
            navigate(`/paymentPlan/pages/success?order_id=${result.data.order_id}&payment_method=alipay&verified=true`);
          }, 2000);

        } else {
          // 验证失败
          const errorMessage = result.error?.message || '支付验证失败';
          // 通知父窗口支付失败
          try {
            if (window.opener) {
              window.opener.postMessage({
                type: 'PAYMENT_FAILED',
                reason: result.error?.type || 'verification_failed',
                status: 'failed',
                message: errorMessage
              }, '*');
            }
          } catch (e) {
            console.log('无法通知父窗口:', e);
          }

          // 延迟跳转到错误页面
          setTimeout(() => {
            navigate(`/paymentPlan/pages/error?reason=verification_failed&message=${encodeURIComponent(errorMessage)}`);
          }, 2000);
        }

      } catch (error: unknown) {
        console.error('验证支付失败:', error);
        const errorMessage = error instanceof Error ? error.message : '未知错误';

        // 通知父窗口验证失败
        try {
          if (window.opener) {
            window.opener.postMessage({
              type: 'PAYMENT_FAILED',
              reason: 'verification_error',
              status: 'failed',
              message: errorMessage
            }, '*');
          }
        } catch (e) {
          console.log('无法通知父窗口:', e);
        }

        // 延迟跳转到错误页面
        setTimeout(() => {
          navigate(`/paymentPlan/pages/error?reason=verification_error&message=${encodeURIComponent(errorMessage)}`);
        }, 2000);
      } finally {
        setLoading(false);
      }
    };

    verifyPayment();
  }, [searchParams, navigate]);

  if (loading) {
    return (
      <Container size="sm" py="xl">
        <Paper shadow="md" p="xl" radius="md" style={{ textAlign: 'center' }}>
          <Loader size="lg" />
          <Title order={3} mt="md">正在验证支付结果...</Title>
          <Text size="sm" c="dimmed" mt="xs">
            请稍候，我们正在确认您的支付状态
          </Text>
        </Paper>
      </Container>
    );
  }

  // 显示验证结果（临时显示，会自动跳转）
  return (
    <Container size="sm" py="xl">
      <Paper shadow="md" p="xl" radius="md" style={{ textAlign: 'center' }}>
        {verificationResult?.data?.verification_success ? (
          <Alert color="green" mb="md">
            <Title order={3} c="green">支付验证成功！</Title>
            <Text size="sm" mt="xs">
              正在跳转到成功页面...
            </Text>
          </Alert>
        ) : (
          <Alert color="red" mb="md">
            <Title order={3} c="red">支付验证失败</Title>
            <Text size="sm" mt="xs">
              {verificationResult?.error?.message || '验证过程中出现错误'}
            </Text>
            <Text size="sm" mt="xs">
              正在跳转到错误页面...
            </Text>
          </Alert>
        )}

        <Text size="xs" c="dimmed" mt="xl">
          如果页面没有自动跳转，请手动关闭此窗口
        </Text>
      </Paper>
    </Container>
  );
}
