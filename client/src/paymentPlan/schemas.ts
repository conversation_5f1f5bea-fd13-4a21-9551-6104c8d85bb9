import { z } from 'zod';

/**
 * 付费计划范围类型
 */
export const ScopeTypeSchema = z.enum(['app', 'course', 'bundle', 'digital_video']);
export type ScopeType = z.infer<typeof ScopeTypeSchema>;

/**
 * 付费计划
 */
export const PaymentPlanSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string().nullable().optional(),
  price: z.number(),
  original_price: z.number(),
  validity_period: z.number(),
  scope_type: ScopeTypeSchema,
  scope_ids: z.array(z.string()),
  is_active: z.boolean(),
  created_at: z.string().nullable().optional(),
  updated_at: z.string().nullable().optional(),
});
export type PaymentPlan = z.infer<typeof PaymentPlanSchema>;
