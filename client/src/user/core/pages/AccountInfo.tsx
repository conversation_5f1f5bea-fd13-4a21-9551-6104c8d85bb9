import React from 'react';
import { Container, Card, Stack, Group, Text, Avatar, Divider, Badge } from '@mantine/core';
import { RiUserLine, RiPhoneLine, RiShieldUserLine, RiGlobalLine } from 'react-icons/ri';
import { useTheme } from '~/core/features/mantine';
import { useQuery } from '@tanstack/react-query';
import { getCurrentUser } from '../api';
import { useUserStore } from '~/user/core/store';

/**
 * 账号信息页面
 */
const AccountInfo: React.FC = () => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const { token } = useUserStore();

  // 获取当前用户信息
  const {
    data: userResponse,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['currentUser'],
    queryFn: () => getCurrentUser(token || ''),
    enabled: !!token,
    refetchOnWindowFocus: false,
  });

  const userInfo = userResponse?.data;

  // 主题颜色
  const bgColor = isDark ? '#1a202c' : 'white';
  const textColor = isDark ? '#e2e8f0' : '#2d3748';
  const secondaryTextColor = isDark ? '#a0aec0' : '#718096';
  const borderColor = isDark ? '#2d3748' : '#e2e8f0';

  if (isLoading) {
    return (
      <Container size="100%" maw="800px" pt={72} pb={72} px="xl">
        <Text>加载中...</Text>
      </Container>
    );
  }

  if (error || !userInfo) {
    return (
      <Container size="100%" maw="800px" pt={72} pb={72} px="xl">
        <Text c="red">获取用户信息失败</Text>
      </Container>
    );
  }

  return (
    <Container size="100%" maw="800px" pt={72} pb={72} px="xl">
      <Stack gap="xl">
        {/* 页面标题 */}
        <Group align="center" gap="md">
          <Avatar size={48} bg="rgba(73, 81, 235, 1)" color="white">
            <RiUserLine size={24} />
          </Avatar>
          <Text size="xl" fw={700} c={textColor}>
            账号信息
          </Text>
        </Group>

        {/* 基本信息卡片 */}
        <Card
          className="rounded-[12px]"
          bg={bgColor}
          p="xl"
          style={{ border: `1px solid ${borderColor}` }}
        >
          <Stack gap="lg">
            <Text size="lg" fw={600} c={textColor}>
              基本信息
            </Text>
            
            <Divider color={borderColor} />

            {/* 用户ID */}
            <Group align="center" gap="md">
              <RiUserLine size={20} color={secondaryTextColor} />
              <Stack gap={4} style={{ flex: 1 }}>
                <Text size="sm" c={secondaryTextColor}>
                  用户ID
                </Text>
                <Text size="md" fw={500} c={textColor}>
                  {userInfo.id}
                </Text>
              </Stack>
            </Group>

            {/* 用户名 */}
            <Group align="center" gap="md">
              <RiUserLine size={20} color={secondaryTextColor} />
              <Stack gap={4} style={{ flex: 1 }}>
                <Text size="sm" c={secondaryTextColor}>
                  用户名
                </Text>
                <Text size="md" fw={500} c={textColor}>
                  {userInfo.username}
                </Text>
              </Stack>
            </Group>

            {/* 昵称 */}
            {userInfo.nickname && (
              <Group align="center" gap="md">
                <RiUserLine size={20} color={secondaryTextColor} />
                <Stack gap={4} style={{ flex: 1 }}>
                  <Text size="sm" c={secondaryTextColor}>
                    昵称
                  </Text>
                  <Text size="md" fw={500} c={textColor}>
                    {userInfo.nickname}
                  </Text>
                </Stack>
              </Group>
            )}

            {/* 手机号 */}
            {userInfo.phone && (
              <Group align="center" gap="md">
                <RiPhoneLine size={20} color={secondaryTextColor} />
                <Stack gap={4} style={{ flex: 1 }}>
                  <Text size="sm" c={secondaryTextColor}>
                    手机号
                  </Text>
                  <Text size="md" fw={500} c={textColor}>
                    {userInfo.phone}
                  </Text>
                </Stack>
              </Group>
            )}
          </Stack>
        </Card>

        {/* 权限信息卡片 */}
        <Card
          className="rounded-[12px]"
          bg={bgColor}
          p="xl"
          style={{ border: `1px solid ${borderColor}` }}
        >
          <Stack gap="lg">
            <Text size="lg" fw={600} c={textColor}>
              权限信息
            </Text>
            
            <Divider color={borderColor} />

            {/* 创建智能体权限 */}
            <Group align="center" gap="md">
              <RiShieldUserLine size={20} color={secondaryTextColor} />
              <Stack gap={4} style={{ flex: 1 }}>
                <Text size="sm" c={secondaryTextColor}>
                  创建智能体权限
                </Text>
                <Badge
                  color={userInfo.can_create_agent ? 'green' : 'gray'}
                  variant="light"
                  size="sm"
                >
                  {userInfo.can_create_agent ? '已开启' : '未开启'}
                </Badge>
              </Stack>
            </Group>

            {/* 公开智能体权限 */}
            <Group align="center" gap="md">
              <RiGlobalLine size={20} color={secondaryTextColor} />
              <Stack gap={4} style={{ flex: 1 }}>
                <Text size="sm" c={secondaryTextColor}>
                  公开智能体权限
                </Text>
                <Badge
                  color={userInfo.is_allow_public_agent ? 'green' : 'gray'}
                  variant="light"
                  size="sm"
                >
                  {userInfo.is_allow_public_agent ? '已开启' : '未开启'}
                </Badge>
              </Stack>
            </Group>
          </Stack>
        </Card>
      </Stack>
    </Container>
  );
};

export default AccountInfo;
