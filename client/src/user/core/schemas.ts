import { z } from 'zod';

/**
 * 用户信息模式
 */
export const UserInfoSchema = z.object({
  id: z.number().describe('用户ID'),
  username: z.string().describe('用户名'),
  nickname: z.string().nullable().optional().describe('用户昵称'),
  phone: z.string().nullable().optional().describe('手机号'),
  can_create_agent: z.boolean().optional().describe('是否可以创建代理'),
  is_allow_public_agent: z.boolean().optional().describe('是否允许公开智能体'),
});

/**
 * 用户账户信息模式
 */
export const UserAccountInfoSchema = z.object({
  balance: z.number().describe('账户余额'),
  remaining_experience_count: z.number().describe('剩余体验次数'),
  remaining_tokens: z.number().describe('剩余token数量'),
  currency: z.string().describe('货币类型'),
});

// 类型导出
export type UserInfo = z.infer<typeof UserInfoSchema>;
export type UserAccountInfo = z.infer<typeof UserAccountInfoSchema>;
