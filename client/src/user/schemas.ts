import { z } from 'zod';

/**
 * 支付方式
 */
export const PaymentMethodSchema = z.enum(['alipay', 'wechatpay']);

/**
 * 资产类型
 */
export const AssetTypeSchema = z.enum(['course', 'app']);
export type AssetType = z.infer<typeof AssetTypeSchema>;

/**
 * 产品类型
 */
export const ProductTypeSchema = z.enum(['app', 'course', 'digital_video']);
export type ProductType = z.infer<typeof ProductTypeSchema>;

/**
 * 创建订单请求
 */
export const OrderCreateRequestSchema = z.object({
  payment_method: PaymentMethodSchema,
  product_type: ProductTypeSchema,
  product_id: z.string(),
  payment_plan_id: z.number(),
  quantity: z.number().default(1),
});
export type OrderCreateRequest = z.infer<typeof OrderCreateRequestSchema>;

/**
 * 订单结果
 */
export const OrderResultSchema = z.object({
  order_id: z.string(),
  payment_url: z.string().nullable().optional(),
  message: z.string(),
});
export type OrderResult = z.infer<typeof OrderResultSchema>;

/**
 * 订单状态枚举
 */
export const OrderStatusSchema = z.enum([
  'PENDING', // 待支付
  'PAID', // 已支付
  'CANCELLED', // 已取消
  'REFUNDED', // 已退款
]);
export type OrderStatus = z.infer<typeof OrderStatusSchema>;

/**
 * 订单
 */
export const OrderSchema = z.object({
  user_id: z.number().int().positive(),
  id: z.string(),
  status: OrderStatusSchema,
  pay_time: z.string(),
  created_at: z.string(),
  updated_at: z.string(),
  amount: z.number().nonnegative(),
  payment_method: PaymentMethodSchema,
  product_type: ProductTypeSchema,
  product_id: z.string(),
  payment_plan_id: z.number(),
  quantity: z.number().default(1),
});
export type Order = z.infer<typeof OrderSchema>;

/**
 * 用户信息
 */
export const UserInfoSchema = z.object({
  id: z.number().int().positive(),
  username: z.string(),
  can_create_agent: z.boolean(),
});
export type UserInfo = z.infer<typeof UserInfoSchema>;

/**
 * 登录结果
 */
export const LoginResultSchema = z.object({
  token: z.string(),
  user_info: UserInfoSchema,
});
export type LoginResult = z.infer<typeof LoginResultSchema>;

/**
 * 登录请求验证模式
 */
export const LoginRequestSchema = z.object({
  username: z.string().min(3, '用户名至少3个字符'),
  password: z.string().min(6, '密码至少6个字符'),
});
export type LoginRequest = z.infer<typeof LoginRequestSchema>;

/**
 * 短信登录请求验证模式
 */
export const SmsLoginRequestSchema = z.object({
  phone: z.string().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号'),
  code: z.string().length(6, '请输入6位验证码'),
});
export type SmsLoginRequest = z.infer<typeof SmsLoginRequestSchema>;

/**
 * 重置密码请求验证模式
 */
export const ResetPasswordRequestSchema = z.object({
  phone: z.string().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号'),
  code: z.string().length(6, '请输入6位验证码'),
  new_password: z
    .string()
    .min(6, '最小长度为6')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{6,}$/, '必须包含大小写字母和数字'),
});
export type ResetPasswordRequest = z.infer<typeof ResetPasswordRequestSchema>;

/**
 * 注册请求验证模式
 */
export const RegisterRequestSchema = z.object({
  username: z.string().min(3, '最小长度为3').max(20, '最大长度为20'),
  password: z
    .string()
    .min(6, '最小长度为6')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{6,}$/, '必须包含大小写字母和数字'),
  phone: z.string().regex(/^1[3-9]\d{9}$/, '请输入正确的手机号'),
  code: z.string().length(6, '请输入6位数字验证码'),
});
export type RegisterRequest = z.infer<typeof RegisterRequestSchema>;

/**
 * 用户账户信息
 */
export const UserAccountInfoSchema = z.object({
  balance: z.number().nonnegative(),
  remaining_experience_count: z.number().nonnegative(),
  remaining_tokens: z.number().nonnegative(),
  currency: z.string(),
});
export type UserAccountInfo = z.infer<typeof UserAccountInfoSchema>;
