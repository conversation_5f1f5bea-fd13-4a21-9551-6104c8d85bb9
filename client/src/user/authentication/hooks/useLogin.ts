import { useState } from 'react';
import { useForm, zodResolver } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { useUserStore } from '~/user/core/store';
// 移除了主页引导功能
import { LoginRequestSchema, SmsLoginRequestSchema } from '../schema';
import { login, smsLogin } from '../api';

// Type for login form values
export type LoginFormValues = {
  username: string;
  password: string;
  agreed: boolean;
};

// Type for SMS login form values
export type SmsLoginFormValues = {
  phone: string;
  code: string;
  agreed: boolean;
};

/**
 * Custom hook for login functionality
 */
export const useLogin = () => {
  const navigate = useNavigate();
  const [countdown, setCountdown] = useState(0);
  const { setToken, setUserInfo, loginMethod, loginTitle: title, setLoginMethod } = useUserStore();

  // Password login form
  const passwordForm = useForm<LoginFormValues>({
    initialValues: {
      username: '',
      password: '',
      agreed: false,
    },
    validate: zodResolver(LoginRequestSchema),
  });

  // SMS login form
  const smsForm = useForm<SmsLoginFormValues>({
    initialValues: {
      phone: '',
      code: '',
      agreed: false,
    },
    validate: zodResolver(SmsLoginRequestSchema),
  });

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: (data: LoginFormValues | SmsLoginFormValues) =>
      loginMethod === 'account' ? login(data as LoginFormValues) : smsLogin(data as SmsLoginFormValues),
    onSuccess: (response) => {
      if (response.data) {
        // Store user info and token
        setToken(response.data.token);
        setUserInfo(response.data.user_info);

        // Show success notification
        notifications.show({
          title: '登录成功',
          message: '欢迎回来',
          color: 'green',
        });

        // Navigate to agent marketplace
        navigate('/agent/marketplace');
      } else if (response.error) {
        // Show error notification
        notifications.show({
          title: '登录失败',
          message: response.error.message || '请检查您的用户名和密码',
          color: 'red',
        });
      }
    },
    onError: (error: Error) => {
      // Show error notification
      notifications.show({
        title: '登录失败',
        message: error.message || '网络错误，请稍后再试',
        color: 'red',
      });
    },
  });

  // Handle login form submission
  const handleLogin = (values: LoginFormValues | SmsLoginFormValues) => {
    // Submit the form values
    loginMutation.mutate(values);
  };

  return {
    loginMethod,
    setLoginMethod,
    countdown,
    setCountdown,
    passwordForm,
    smsForm,
    handleLogin,
    isLoading: loginMutation.isPending,
    title,
  };
};

export default useLogin;
