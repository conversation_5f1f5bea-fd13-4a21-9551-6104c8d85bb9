import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Title,
  Text,
  Group,
  Button,
  SimpleGrid,
  Modal,
  Stack,
  Center,
  Loader,
  TextInput,
  Textarea,
  Tabs,
  Select,
  Table,
  ActionIcon,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { RiEdit2Line, RiDeleteBin6Line } from 'react-icons/ri';
import { KnowledgeUploader, KnowledgeCard, CreateKnowledgeCard } from '../components';
import SimpleSearchBox from '../components/SimpleSearchBox';
import {
  getKnowledgeList,
  deleteKnowledge,
  updateKnowledge,
  getMetadataList,
  createMetadata,
  updateMetadata,
  deleteMetadata,
  toggleBuiltInMetadata,
} from '../api';
import { KnowledgeDataset, KnowledgeCreateResponse, MetadataField } from '../schemas';

/**
 * 知识库页面组件
 */
const KnowledgeBase: React.FC = () => {
  // 基本状态
  const navigate = useNavigate();
  const [opened, { open, close }] = useDisclosure(false);
  const [editModalOpened, { open: openEditModal, close: closeEditModal }] = useDisclosure(false);
  const [metadataModalOpened, { close: closeMetadataModal }] = useDisclosure(false);
  const [currentKnowledge, setCurrentKnowledge] = useState<KnowledgeDataset | null>(null);
  const [editName, setEditName] = useState('');
  const [editDescription, setEditDescription] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');
  const queryClient = useQueryClient();

  // 元数据相关状态
  const [activeTab, setActiveTab] = useState<string | null>('basic');
  const [newMetadataType, setNewMetadataType] = useState<string>('string');
  const [newMetadataName, setNewMetadataName] = useState<string>('');
  const [editMetadataId, setEditMetadataId] = useState<string | null>(null);
  const [editMetadataName, setEditMetadataName] = useState<string>('');
  const [isCreatingMetadata, setIsCreatingMetadata] = useState<boolean>(false);
  const [isUpdatingMetadata, setIsUpdatingMetadata] = useState<boolean>(false);
  const [isDeletingMetadata, setIsDeletingMetadata] = useState<boolean>(false);
  const [isTogglingBuiltIn, setIsTogglingBuiltIn] = useState<boolean>(false);

  // 获取知识库列表
  const { data, isLoading, error } = useQuery({
    queryKey: ['knowledgeList', searchKeyword],
    queryFn: () =>
      getKnowledgeList({
        page: 1,
        page_size: 20,
        keyword: searchKeyword || undefined,
      }),
  });

  // 获取元数据列表
  const {
    data: metadataData,
    isLoading: isLoadingMetadata,
    error: metadataError,
    refetch: refetchMetadata,
  } = useQuery({
    queryKey: ['metadataList', currentKnowledge?.id],
    queryFn: () => (currentKnowledge ? getMetadataList(currentKnowledge.id) : Promise.resolve(null)),
    enabled: !!currentKnowledge && metadataModalOpened,
  });

  // 处理知识库创建成功
  const handleKnowledgeCreated = (data: KnowledgeCreateResponse) => {
    console.log('知识库创建成功，准备刷新列表:', data);
    // 关闭模态框
    close();
    // 刷新知识库列表
    queryClient
      .invalidateQueries({ queryKey: ['knowledgeList'] })
      .then(() => {
        console.log('知识库列表刷新成功');
      })
      .catch((error) => {
        console.error('知识库列表刷新失败:', error);
      });
  };

  // 处理知识库点击
  const handleKnowledgeClick = (knowledge: KnowledgeDataset) => {
    // 跳转到知识库详情页面
    navigate(`/knowledge/${knowledge.id}`);
  };

  // 处理知识库编辑
  const handleEditClick = (knowledge: KnowledgeDataset) => {
    setCurrentKnowledge(knowledge);
    setEditName(knowledge.name);
    setEditDescription(knowledge.description || '');
    openEditModal();
  };

  // 处理知识库更新
  const handleUpdateKnowledge = async () => {
    if (!currentKnowledge) return;

    setIsUpdating(true);

    try {
      await updateKnowledge(currentKnowledge.id, {
        name: editName,
        description: editDescription,
      });

      notifications.show({
        title: '成功',
        message: '知识库更新成功',
        color: 'green',
      });

      // 刷新知识库列表
      queryClient.invalidateQueries({ queryKey: ['knowledgeList'] });

      // 关闭编辑模态框
      closeEditModal();
    } catch (error) {
      notifications.show({
        title: '错误',
        message: error instanceof Error ? error.message : '更新知识库失败',
        color: 'red',
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // 处理知识库删除
  const handleKnowledgeDelete = async (id: string) => {
    try {
      await deleteKnowledge(id);
      notifications.show({
        title: '成功',
        message: '知识库删除成功',
        color: 'green',
      });
      // 刷新知识库列表
      queryClient.invalidateQueries({ queryKey: ['knowledgeList'] });
    } catch (error) {
      notifications.show({
        title: '错误',
        message: error instanceof Error ? error.message : '删除知识库失败',
        color: 'red',
      });
    }
  };

  // 处理搜索
  const handleSearch = async () => {
    // 搜索逻辑已经在useQuery中处理
    // 返回一个空的结果以满足类型要求
    return {
      data: [],
      error: null,
      isError: false,
      isLoading: false,
      isSuccess: true,
      status: 'success' as const,
      refetch: async () => handleSearch(),
    };
  };

  // 元数据管理功能已移除

  // 创建元数据字段
  const handleCreateMetadata = async () => {
    if (!currentKnowledge || !newMetadataName.trim()) return;

    setIsCreatingMetadata(true);

    try {
      await createMetadata(currentKnowledge.id, {
        type: newMetadataType,
        name: newMetadataName,
      });

      notifications.show({
        title: '成功',
        message: '元数据字段创建成功',
        color: 'green',
      });

      // 重置表单
      setNewMetadataName('');

      // 刷新元数据列表
      refetchMetadata();
    } catch (error) {
      notifications.show({
        title: '错误',
        message: error instanceof Error ? error.message : '创建元数据字段失败',
        color: 'red',
      });
    } finally {
      setIsCreatingMetadata(false);
    }
  };

  // 更新元数据字段
  const handleUpdateMetadata = async () => {
    if (!currentKnowledge || !editMetadataId || !editMetadataName.trim()) return;

    setIsUpdatingMetadata(true);

    try {
      await updateMetadata(currentKnowledge.id, editMetadataId, {
        name: editMetadataName,
      });

      notifications.show({
        title: '成功',
        message: '元数据字段更新成功',
        color: 'green',
      });

      // 重置表单
      setEditMetadataId(null);
      setEditMetadataName('');

      // 刷新元数据列表
      refetchMetadata();
    } catch (error) {
      notifications.show({
        title: '错误',
        message: error instanceof Error ? error.message : '更新元数据字段失败',
        color: 'red',
      });
    } finally {
      setIsUpdatingMetadata(false);
    }
  };

  // 删除元数据字段
  const handleDeleteMetadata = async (metadataId: string) => {
    if (!currentKnowledge) return;

    if (!window.confirm('确定要删除此元数据字段吗？此操作不可撤销。')) {
      return;
    }

    setIsDeletingMetadata(true);

    try {
      await deleteMetadata(currentKnowledge.id, metadataId);

      notifications.show({
        title: '成功',
        message: '元数据字段删除成功',
        color: 'green',
      });

      // 刷新元数据列表
      refetchMetadata();
    } catch (error) {
      notifications.show({
        title: '错误',
        message: error instanceof Error ? error.message : '删除元数据字段失败',
        color: 'red',
      });
    } finally {
      setIsDeletingMetadata(false);
    }
  };

  // 启用/禁用内置字段
  const handleToggleBuiltInField = async (action: 'enable' | 'disable') => {
    if (!currentKnowledge) return;

    setIsTogglingBuiltIn(true);

    try {
      await toggleBuiltInMetadata(currentKnowledge.id, action);

      notifications.show({
        title: '成功',
        message: `内置字段${action === 'enable' ? '启用' : '禁用'}成功`,
        color: 'green',
      });

      // 刷新元数据列表
      refetchMetadata();
    } catch (error) {
      notifications.show({
        title: '错误',
        message: error instanceof Error ? error.message : `${action === 'enable' ? '启用' : '禁用'}内置字段失败`,
        color: 'red',
      });
    } finally {
      setIsTogglingBuiltIn(false);
    }
  };

  // 准备编辑元数据字段
  const handlePrepareEditMetadata = (metadata: MetadataField) => {
    setEditMetadataId(metadata.id);
    setEditMetadataName(metadata.name);
  };

  // 取消编辑元数据字段
  const handleCancelEditMetadata = () => {
    setEditMetadataId(null);
    setEditMetadataName('');
  };

  return (
    <Container size="lg" py={40}>
      <Group justify="space-between" mb={30}>
        <Title order={2}>我的知识库</Title>
      </Group>

      <Center mb={30}>
        <SimpleSearchBox
          placeholder="搜索知识库..."
          searchKeyword={searchKeyword}
          setSearchKeyword={setSearchKeyword}
          onSearch={() => handleSearch()}
          width="682px"
        />
      </Center>

      {isLoading ? (
        <Center h={200}>
          <Loader size="md" color="blue" />
        </Center>
      ) : error ? (
        <Text c="red" ta="center">
          加载失败: {error instanceof Error ? error.message : '未知错误'}
        </Text>
      ) : data?.data?.data?.length === 0 ? (
        <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="md">
          {/* 创建知识库卡片 */}
          <div data-tour="knowledge-upload">
            <CreateKnowledgeCard onClick={open} />
          </div>
        </SimpleGrid>
      ) : (
        <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="md">
          {/* 创建知识库卡片 */}
          <div data-tour="knowledge-upload">
            <CreateKnowledgeCard onClick={open} />
          </div>

          {/* 知识库列表 */}
          {data?.data?.data?.map((knowledge: KnowledgeDataset, index: number) => (
            <div key={knowledge.id} data-tour={index === 0 ? "knowledge-list" : undefined}>
              <KnowledgeCard
                knowledge={knowledge}
                onClick={handleKnowledgeClick}
                onDelete={handleKnowledgeDelete}
                onEdit={handleEditClick}
              />
            </div>
          ))}
        </SimpleGrid>
      )}

      {/* 创建知识库模态框 */}
      <Modal opened={opened} onClose={close} title="创建知识库" size="lg">
        <KnowledgeUploader onKnowledgeCreated={handleKnowledgeCreated} />
      </Modal>

      {/* 编辑知识库模态框 */}
      <Modal opened={editModalOpened} onClose={closeEditModal} title="编辑知识库" size="md">
        <Stack>
          <TextInput
            label="知识库名称"
            placeholder="输入知识库名称"
            value={editName}
            onChange={(e) => setEditName(e.target.value)}
            required
          />

          <Textarea
            label="知识库描述"
            placeholder="输入知识库描述（可选）"
            value={editDescription}
            onChange={(e) => setEditDescription(e.target.value)}
            autosize
            minRows={3}
            maxRows={5}
          />

          <Group justify="flex-end" mt="md">
            <Button variant="default" onClick={closeEditModal}>
              取消
            </Button>
            <Button color="blue" onClick={handleUpdateKnowledge} loading={isUpdating} disabled={!editName.trim()}>
              保存
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* 元数据管理模态框 */}
      <Modal
        opened={metadataModalOpened}
        onClose={closeMetadataModal}
        title={`管理元数据 - ${currentKnowledge?.name || ''}`}
        size="lg"
      >
        <Tabs value={activeTab} onChange={setActiveTab}>
          <Tabs.List>
            <Tabs.Tab value="basic">基本信息</Tabs.Tab>
            <Tabs.Tab value="metadata">元数据字段</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="basic" pt="md">
            <Stack>
              <Text fw={500}>知识库ID: {currentKnowledge?.id}</Text>
              <Text>名称: {currentKnowledge?.name}</Text>
              <Text>描述: {currentKnowledge?.description || '无'}</Text>
              <Text>文档数量: {currentKnowledge?.document_count || 0}</Text>
              <Text>
                创建时间: {currentKnowledge?.created_at ? new Date(currentKnowledge.created_at * 1000).toLocaleString() : '未知'}
              </Text>
            </Stack>
          </Tabs.Panel>

          <Tabs.Panel value="metadata" pt="md">
            {isLoadingMetadata ? (
              <Center h={200}>
                <Loader size="md" color="blue" />
              </Center>
            ) : metadataError ? (
              <Text c="red" ta="center">
                加载失败: {metadataError instanceof Error ? metadataError.message : '未知错误'}
              </Text>
            ) : (
              <Stack>
                {/* 内置字段控制 */}
                <Group position="apart">
                  <Text fw={500}>内置字段</Text>
                  <Group>
                    <Button
                      size="xs"
                      variant="outline"
                      color="green"
                      onClick={() => handleToggleBuiltInField('enable')}
                      loading={isTogglingBuiltIn}
                      disabled={metadataData?.data?.built_in_field_enabled}
                    >
                      启用
                    </Button>
                    <Button
                      size="xs"
                      variant="outline"
                      color="red"
                      onClick={() => handleToggleBuiltInField('disable')}
                      loading={isTogglingBuiltIn}
                      disabled={!metadataData?.data?.built_in_field_enabled}
                    >
                      禁用
                    </Button>
                  </Group>
                </Group>

                {/* 创建新元数据字段 */}
                <Stack>
                  <Text fw={500}>创建新元数据字段</Text>
                  <Group>
                    <Select
                      label="字段类型"
                      placeholder="选择字段类型"
                      value={newMetadataType}
                      onChange={(value) => setNewMetadataType(value || 'string')}
                      data={[
                        { value: 'string', label: '文本' },
                        { value: 'number', label: '数字' },
                        { value: 'boolean', label: '布尔值' },
                        { value: 'date', label: '日期' },
                      ]}
                      style={{ width: '150px' }}
                    />
                    <TextInput
                      label="字段名称"
                      placeholder="输入字段名称"
                      value={newMetadataName}
                      onChange={(e) => setNewMetadataName(e.target.value)}
                      style={{ flex: 1 }}
                    />
                    <Button
                      mt={24}
                      onClick={handleCreateMetadata}
                      loading={isCreatingMetadata}
                      disabled={!newMetadataName.trim()}
                    >
                      创建
                    </Button>
                  </Group>
                </Stack>

                {/* 元数据字段列表 */}
                <Stack>
                  <Text fw={500}>元数据字段列表</Text>
                  <Table>
                    <Table.Thead>
                      <Table.Tr>
                        <Table.Th>ID</Table.Th>
                        <Table.Th>名称</Table.Th>
                        <Table.Th>类型</Table.Th>
                        <Table.Th>使用次数</Table.Th>
                        <Table.Th>操作</Table.Th>
                      </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody>
                      {metadataData?.data?.doc_metadata?.length === 0 ? (
                        <Table.Tr>
                          <Table.Td colSpan={5} align="center">
                            暂无元数据字段
                          </Table.Td>
                        </Table.Tr>
                      ) : (
                        metadataData?.data?.doc_metadata?.map((metadata) => (
                          <Table.Tr key={metadata.id}>
                            <Table.Td>{metadata.id}</Table.Td>
                            <Table.Td>
                              {editMetadataId === metadata.id ? (
                                <TextInput
                                  value={editMetadataName}
                                  onChange={(e) => setEditMetadataName(e.target.value)}
                                  size="xs"
                                />
                              ) : (
                                metadata.name
                              )}
                            </Table.Td>
                            <Table.Td>{metadata.type}</Table.Td>
                            <Table.Td>{metadata.use_count || 0}</Table.Td>
                            <Table.Td>
                              {editMetadataId === metadata.id ? (
                                <Group>
                                  <ActionIcon
                                    color="green"
                                    onClick={handleUpdateMetadata}
                                    loading={isUpdatingMetadata}
                                    disabled={!editMetadataName.trim()}
                                  >
                                    <RiEdit2Line size={16} />
                                  </ActionIcon>
                                  <ActionIcon color="gray" onClick={handleCancelEditMetadata}>
                                    ✕
                                  </ActionIcon>
                                </Group>
                              ) : (
                                <Group>
                                  <ActionIcon color="blue" onClick={() => handlePrepareEditMetadata(metadata)}>
                                    <RiEdit2Line size={16} />
                                  </ActionIcon>
                                  <ActionIcon
                                    color="red"
                                    onClick={() => handleDeleteMetadata(metadata.id)}
                                    loading={isDeletingMetadata}
                                  >
                                    <RiDeleteBin6Line size={16} />
                                  </ActionIcon>
                                </Group>
                              )}
                            </Table.Td>
                          </Table.Tr>
                        ))
                      )}
                    </Table.Tbody>
                  </Table>
                </Stack>
              </Stack>
            )}
          </Tabs.Panel>
        </Tabs>
      </Modal>
    </Container>
  );
};

export default KnowledgeBase;
