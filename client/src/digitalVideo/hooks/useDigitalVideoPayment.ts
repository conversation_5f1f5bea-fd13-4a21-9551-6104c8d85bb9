import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { notifications } from '@mantine/notifications';
import { getPaymentPlans } from '~/paymentPlan/api';
import { PaymentPlan } from '~/paymentPlan/schemas';
import { usePaymentMutation, usePaymentMessageListener } from '~/core/utils/payment';

/**
 * 数字人支付Hook
 * 用于处理数字人购买支付流程
 */
export const useDigitalVideoPayment = (onPaymentSuccess?: () => void) => {
  const navigate = useNavigate();
  // 支付方式
  const [paymentMethod, setPaymentMethod] = useState<'alipay' | 'wechatpay'>('alipay');
  // 订单ID
  const [orderId, setOrderId] = useState<string>('');
  // 选中的付费计划ID
  const [selectedPlanId, setSelectedPlanId] = useState<number | undefined>(undefined);

  // 获取付费计划列表 - 数字人使用DIGITAL_VIDEO类型
  const { data: paymentPlansResponse, isLoading: isPaymentPlansLoading } = useQuery({
    queryKey: ['paymentPlans', 'digitalVideo', 'digital-video-service'],
    queryFn: () => getPaymentPlans('digital_video', 'digital-video-service'),
  });

  // 提取付费计划数据
  const paymentPlans = paymentPlansResponse?.data || [];

  // 获取选中的付费计划
  const selectedPlan = paymentPlans.find((plan) => plan.id === selectedPlanId);

  // 处理选择付费计划
  const handleSelectPlan = (plan: PaymentPlan) => {
    setSelectedPlanId(plan.id);
  };

  // 支付成功处理
  const handlePaymentSuccess = () => {
    // 如果有传入的回调函数，先执行回调（比如关闭弹窗）
    if (onPaymentSuccess) {
      onPaymentSuccess();
    }

    // 延迟一点时间后刷新页面，让用户看到成功提示
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  // 支付处理逻辑
  const handlePayment = usePaymentMutation(setOrderId, navigate, handlePaymentSuccess);

  // 支付消息监听（支持支付宝和微信支付）
  const [isListeningEnabled, setIsListeningEnabled] = useState(false);

  usePaymentMessageListener(
    orderId,
    isListeningEnabled,
    () => {
      setIsListeningEnabled(false);
      // 显示购买成功通知
      notifications.show({
        title: '购买成功',
        message: '数字人服务开通成功，您现在可以使用数字人功能了！',
        color: 'green',
      });
      handlePaymentSuccess();
    },
    () => {
      setIsListeningEnabled(false);
    }
  );

  // 处理数字人购买
  const handleDigitalVideoPurchase = (paymentMethodOverride?: 'alipay' | 'wechatpay') => {
    const currentPaymentMethod = paymentMethodOverride || paymentMethod;

    if (!currentPaymentMethod) {
      notifications.show({
        title: '请选择支付方式',
        message: '请先选择支付宝或微信支付',
        color: 'yellow',
      });
      return;
    }

    if (!selectedPlanId) {
      notifications.show({
        title: '请选择付费计划',
        message: '请先选择一个付费计划',
        color: 'yellow',
      });
      return;
    }

    // 创建订单请求 - 数字人使用固定的产品ID
    handlePayment.mutate({
      product_type: 'digital_video',
      product_id: 'digital-video-service', // 数字人服务的固定ID
      payment_plan_id: selectedPlanId,
      quantity: 1,
      selectedPaymentMethod: currentPaymentMethod,
    });

    // 启用消息监听（支付宝通过回调，微信支付通过轮询后的postMessage）
    setIsListeningEnabled(true);
  };

  return {
    paymentMethod,
    setPaymentMethod,
    handleDigitalVideoPurchase,
    isPaymentLoading: handlePayment.isPending,
    selectedPlan,
    paymentPlans,
    selectedPlanId,
    handleSelectPlan,
    isPaymentPlansLoading,
  };
};
