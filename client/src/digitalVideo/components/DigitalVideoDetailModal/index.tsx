import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';

interface DigitalVideoDetailModalProps {
  /**
   * 是否打开弹框
   */
  opened: boolean;
  /**
   * 关闭弹框的回调函数
   */
  onClose: () => void;
}

/**
 * 数字人详情弹框组件
 * 根据设备类型自动选择桌面版或移动版
 */
const DigitalVideoDetailModal: React.FC<DigitalVideoDetailModalProps> = (props) => {
  const { isMobile } = useDeviceDetect();

  // 根据设备类型动态选择适当的组件
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default DigitalVideoDetailModal;
