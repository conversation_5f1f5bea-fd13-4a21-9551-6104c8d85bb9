import React from 'react';
import { Modal, Image, Text, Stack, List } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { useDigitalVideoPayment } from '../../hooks/useDigitalVideoPayment';
import { FaAlipay, FaWeixin } from 'react-icons/fa';
import { PaymentPlanList } from '~/paymentPlan/components';
import './styles.css';

interface MobileDigitalVideoDetailModalProps {
  /**
   * 是否打开弹框
   */
  opened: boolean;
  /**
   * 关闭弹框的回调函数
   */
  onClose: () => void;
}

/**
 * 数字人详情弹框组件 - 移动版
 */
const Mobile: React.FC<MobileDigitalVideoDetailModalProps> = ({ opened, onClose }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  // 使用支付Hook，传入关闭弹窗的回调
  const {
    handleDigitalVideoPurchase,
    isPaymentLoading,
    selectedPlan,
    paymentPlans,
    selectedPlanId,
    handleSelectPlan,
    isPaymentPlansLoading,
  } = useDigitalVideoPayment(onClose);

  // 静态的数字人信息
  const digitalVideoDetail = {
    name: '数字人服务',
    description: '提供数字人视频生成服务，支持自定义形象和语音，适用于内容创作和营销推广。',
    icon: 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/myedit_ai_image_0604191318.jpg',
    creator: '智元平台',
    features: [
      '定制数字人形象',
      '定制声音',
      '高质量视频合成输出',
      '打造你的数字分身',
    ],
    applicationScenarios: [
      '内容获客 - 营销推广、品牌宣传',
      '教育培训 - 课程讲解、知识分享',
      '产品介绍 - 商品展示、功能说明',
      '企业宣传 - 公司介绍、团队展示',
    ],
  };

  return (
    <Modal.Root opened={opened} onClose={onClose} fullScreen className="digital-video-detail-modal">
      <Modal.Overlay />
      <Modal.Content>
        <Modal.Header>
          <Modal.Title>数字人服务详情</Modal.Title>
          <Modal.CloseButton />
        </Modal.Header>
        <div className="flex flex-col h-full overflow-auto">
          <div className="flex-1 overflow-auto p-4">
            {/* 数字人头像和基本信息 */}
            <div className="flex flex-col items-center mb-6">
              <div className="w-24 h-24 rounded-full overflow-hidden mb-4">
                <Image
                  src={digitalVideoDetail.icon}
                  alt={digitalVideoDetail.name}
                  width={96}
                  height={96}
                  fit="cover"
                />
              </div>
              <Text size="xl" fw={600} mb="xs" ta="center">
                {digitalVideoDetail.name}
              </Text>
              <Text size="sm" c="dimmed" mb="sm" ta="center">
                创建者：{digitalVideoDetail.creator}
              </Text>
              <Text size="sm" c="dimmed" ta="center" px="md">
                {digitalVideoDetail.description}
              </Text>
            </div>

            {/* 应用场景 */}
            <Stack gap="md" className="mt-4">
              <Text size="lg" fw={600}>
                应用场景
              </Text>
              <div className="grid grid-cols-1 gap-3">
                {digitalVideoDetail.applicationScenarios.map((scenario, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-lg border ${
                      isDark
                        ? 'border-gray-600 bg-gray-800'
                        : 'border-gray-200 bg-gray-50'
                    }`}
                  >
                    <Text size="sm">{scenario}</Text>
                  </div>
                ))}
              </div>
            </Stack>

            {/* 付费计划列表 */}
            <PaymentPlanList
              plans={paymentPlans}
              isLoading={isPaymentPlansLoading}
              selectedPlanId={selectedPlanId}
              onSelectPlan={handleSelectPlan}
            />

            {/* 功能列表 */}
            <Stack gap="md" className="mt-4">
              <Text size="lg" fw={600}>
                服务功能
              </Text>
              <List spacing="xs" size="sm" center>
                {digitalVideoDetail.features.map((feature, index) => (
                  <List.Item key={index}>
                    <Text size="sm">{feature}</Text>
                  </List.Item>
                ))}
              </List>
            </Stack>
          </div>

          {/* 底部支付按钮 - 固定在底部 */}
          <div
            className="sticky bottom-0 left-0 right-0 z-20 p-4 mt-auto"
            style={{
              backgroundColor: isDark ? '#1a202c' : 'white',
            }}
          >
            <div className="flex flex-col gap-3">
              <button
                className={`flex items-center justify-center gap-2 w-full py-3 rounded-full font-medium ${
                  isDark ? 'bg-gray-800 hover:bg-gray-700' : 'bg-black hover:bg-gray-800'
                } text-white`}
                onClick={() => handleDigitalVideoPurchase('alipay')}
                disabled={isPaymentLoading || !selectedPlanId}
              >
                {isPaymentLoading ? (
                  <span>处理中...</span>
                ) : (
                  <>
                    <FaAlipay size={20} color="#1677FF" />
                    支付宝 ¥{selectedPlan?.price || 0}
                  </>
                )}
              </button>
              <button
                className={`flex items-center justify-center gap-2 w-full py-3 rounded-full font-medium ${
                  isDark ? 'bg-gray-800 hover:bg-gray-700' : 'bg-black hover:bg-gray-800'
                } text-white`}
                onClick={() => handleDigitalVideoPurchase('wechatpay')}
                disabled={isPaymentLoading || !selectedPlanId}
              >
                {isPaymentLoading ? (
                  <span>处理中...</span>
                ) : (
                  <>
                    <FaWeixin size={20} color="#09BB07" />
                    微信 ¥{selectedPlan?.price || 0}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </Modal.Content>
    </Modal.Root>
  );
};

export default Mobile;
