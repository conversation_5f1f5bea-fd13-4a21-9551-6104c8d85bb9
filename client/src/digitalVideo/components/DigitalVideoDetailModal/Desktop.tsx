import React from 'react';
import { Modal, Image, Text, Stack, List } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { useDigitalVideoPayment } from '../../hooks/useDigitalVideoPayment';
import { FaAlipay, FaWeixin } from 'react-icons/fa';
import { PaymentPlanList } from '~/paymentPlan/components';
import './styles.css';

interface DesktopDigitalVideoDetailModalProps {
  /**
   * 是否打开弹框
   */
  opened: boolean;
  /**
   * 关闭弹框的回调函数
   */
  onClose: () => void;
}

/**
 * 数字人详情弹框组件 - 桌面版
 */
const Desktop: React.FC<DesktopDigitalVideoDetailModalProps> = ({ opened, onClose }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  console.log('DigitalVideoDetailModal Desktop - opened:', opened);

  // 使用支付Hook，传入关闭弹窗的回调
  const {
    handleDigitalVideoPurchase,
    isPaymentLoading,
    selectedPlan,
    paymentPlans,
    selectedPlanId,
    handleSelectPlan,
    isPaymentPlansLoading,
  } = useDigitalVideoPayment(onClose);

  // 静态的数字人信息
  const digitalVideoDetail = {
    name: '数字人服务',
    description: '提供数字人视频生成服务，支持自定义形象和语音，适用于内容创作和营销推广。',
    icon: 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/myedit_ai_image_0604191318.jpg',
    creator: '智元平台',
    features: [
      '定制数字人形象',
      '定制声音',
      '高质量视频合成输出',
      '打造你的数字分身',
    ],
    applicationScenarios: [
      '内容获客 - 营销推广、品牌宣传',
      '教育培训 - 课程讲解、知识分享',
      '产品介绍 - 商品展示、功能说明',
      '企业宣传 - 公司介绍、团队展示',
    ],
  };

  return (
    <Modal.Root
      opened={opened}
      onClose={onClose}
      size="900px"
      className="digital-video-detail-modal"
      centered
    >
      <Modal.Overlay />
      <Modal.Content>
        <Modal.Header>
          <Modal.Title>数字人服务详情</Modal.Title>
          <Modal.CloseButton />
        </Modal.Header>
        <Modal.Body style={{ padding: 0, display: 'flex', flexDirection: 'column', height: '70vh' }}>
          {/* 主要内容区域 - 可滚动 */}
          <div className="flex-1 overflow-y-auto px-6 py-4">
            {/* 数字人信息 */}
            <div className="flex flex-col items-center text-center mb-6">
              {/* 数字人图标 */}
              <div className="mb-4">
                <div className="digital-video-shadow-stroke overflow-hidden rounded-full">
                  <Image
                    radius={80}
                    w={80}
                    h={80}
                    src={digitalVideoDetail.icon}
                    alt={digitalVideoDetail.name}
                  />
                </div>
              </div>

              {/* 数字人名称和描述 */}
              <Text size="xl" fw={600} mb="xs">
                {digitalVideoDetail.name}
              </Text>
              <Text size="sm" c="dimmed" mb="sm">
                创建者：{digitalVideoDetail.creator}
              </Text>
              <Text size="sm" c="dimmed" style={{ maxWidth: '400px' }}>
                {digitalVideoDetail.description}
              </Text>
            </div>

            {/* 应用场景 */}
            <Stack gap="md" className="mb-6">
              <Text size="lg" fw={600}>
                应用场景
              </Text>
              <div className="grid grid-cols-2 gap-3">
                {digitalVideoDetail.applicationScenarios.map((scenario, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-lg border ${
                      isDark
                        ? 'border-gray-600 bg-gray-800'
                        : 'border-gray-200 bg-gray-50'
                    }`}
                  >
                    <Text size="sm">{scenario}</Text>
                  </div>
                ))}
              </div>
            </Stack>

            {/* 付费计划列表 */}
            <div className="mb-6">
              <PaymentPlanList
                plans={paymentPlans}
                isLoading={isPaymentPlansLoading}
                selectedPlanId={selectedPlanId}
                onSelectPlan={handleSelectPlan}
              />
            </div>

            {/* 功能列表 */}
            <Stack gap="md" className="mb-6">
              <Text size="lg" fw={600}>
                服务功能
              </Text>
              <List spacing="xs" size="sm" center>
                {digitalVideoDetail.features.map((feature, index) => (
                  <List.Item key={index}>
                    <Text size="sm">{feature}</Text>
                  </List.Item>
                ))}
              </List>
            </Stack>
          </div>

          {/* 底部支付按钮 - 固定在底部 */}
          <div
            className="border-t p-4"
            style={{
              backgroundColor: isDark ? '#1a202c' : 'white',
              borderColor: isDark ? '#374151' : '#e5e7eb',
            }}
          >
            <div className="flex flex-col gap-3">
              <button
                className={`flex items-center justify-center gap-2 w-full py-3 rounded-lg font-medium transition-colors ${
                  isDark ? 'bg-gray-800 hover:bg-gray-700' : 'bg-black hover:bg-gray-800'
                } text-white`}
                onClick={() => handleDigitalVideoPurchase('alipay')}
                disabled={isPaymentLoading || !selectedPlanId}
              >
                {isPaymentLoading ? (
                  <span>处理中...</span>
                ) : (
                  <>
                    <FaAlipay size={20} color="#1677FF" />
                    支付宝 ¥{selectedPlan?.price || 0}
                  </>
                )}
              </button>
              <button
                className={`flex items-center justify-center gap-2 w-full py-3 rounded-lg font-medium transition-colors ${
                  isDark ? 'bg-gray-800 hover:bg-gray-700' : 'bg-black hover:bg-gray-800'
                } text-white`}
                onClick={() => handleDigitalVideoPurchase('wechatpay')}
                disabled={isPaymentLoading || !selectedPlanId}
              >
                {isPaymentLoading ? (
                  <span>处理中...</span>
                ) : (
                  <>
                    <FaWeixin size={20} color="#09BB07" />
                    微信 ¥{selectedPlan?.price || 0}
                  </>
                )}
              </button>
            </div>
          </div>
        </Modal.Body>
      </Modal.Content>
    </Modal.Root>
  );
};

export default Desktop;
