/* 数字人详情弹框样式 */
.digital-video-detail-modal .mantine-Modal-content {
  border-radius: 16px;
  overflow: hidden;
}

.digital-video-detail-modal .mantine-Modal-header {
  padding: 20px 24px 0;
  border-bottom: none;
}

.digital-video-detail-modal .mantine-Modal-title {
  font-size: 18px;
  font-weight: 600;
}

.digital-video-detail-modal .mantine-Modal-close {
  width: 32px;
  height: 32px;
  border-radius: 8px;
}

/* 数字人图标阴影效果 */
.digital-video-shadow-stroke {
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

/* 响应式布局 */
@media (max-width: 768px) {
  .digital-video-detail-modal .mantine-Modal-content {
    margin: 0;
    height: 100vh;
    border-radius: 0;
  }

  .digital-video-detail-modal .mantine-Modal-header {
    padding: 16px 20px 0;
  }
}
