import { Box, Stack } from '@mantine/core';
import { ChatHeader, ChatBox } from '../components';
import MessageList from '../components/MessageList';
import { useChatContext } from '../contexts';
import { useParams } from 'react-router-dom';
import { useEffect } from 'react';
import db from '../db';

const Conversation = () => {
  const { id: conversationId } = useParams<{ id: string }>();
  const { conversation, setConversation, setAgent, agentDetail } = useChatContext();

  // 当进入对话页面时，如果聊天上下文中没有对话，就从url中拿到对话id,然后从数据库中加载并添加到上下文
  useEffect(() => {
    const loadConversationFromDatabase = async () => {
      // 如果已经有对话或者没有对话ID，则不需要加载
      if (conversation?.id || !conversationId) return;

      try {
        // 从数据库中获取对话
        const conversationData = await db.conversations.get(conversationId);

        if (conversationData) {
          // 设置对话到上下文
          setConversation(conversationData);

          // 设置智能体到上下文
          if (conversationData.agent) {
            setAgent(conversationData.agent);
          }
        }
      } catch (error) {
        console.error('从数据库加载对话失败:', error);
      }
    };

    loadConversationFromDatabase();
  }, [conversationId]);

  return (
    <Stack className="h-screen overflow-hidden" gap={0}>
      <ChatHeader title={conversation?.name ?? agentDetail?.name ?? '未提供会话名称'} />

      <MessageList data-tour="chat-history" />

      <Box className="flex-none m-auto" w={936} px="var(--mantine-spacing-md)" pb="md">
        <ChatBox />
      </Box>
    </Stack>
  );
};

export default Conversation;
Conversation.displayName = 'Conversation';
