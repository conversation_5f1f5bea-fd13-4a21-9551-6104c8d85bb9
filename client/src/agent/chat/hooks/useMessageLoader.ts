import { useEffect, useRef, useState } from 'react';
import { useChatContext } from '../contexts';
import db from '../db';
import { Message } from '../schemas';

/**
 * 消息加载钩子
 * 用于处理消息的加载、分页和滚动加载
 */
export const useMessageLoader = () => {
  const { conversation, messages, setMessages, scrollToBottomRef } = useChatContext();
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const prevMessagesLengthRef = useRef(0);
  const prevLastMessageContentRef = useRef<string>('');

  // 初始加载最近的10条消息
  useEffect(() => {
    if (conversation?.id && messages.length === 0) {
      loadRecentMessages();
    }
  }, [conversation?.id, messages]);

  // 加载最近的消息
  const loadRecentMessages = async () => {
    if (!conversation?.id) return;

    setIsLoading(true);
    try {
      // 从数据库加载最近的10条消息
      const recentMessages = await db.messages.where('conversation_id').equals(conversation.id).reverse().sortBy('created_at');

      // 只取最近的10条
      const latestMessages = recentMessages.slice(0, 10).reverse();

      setMessages(latestMessages);
      setHasMore(recentMessages.length > 10);
    } catch (error) {
      console.error('加载最近消息失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 加载更多历史消息
  const loadMoreMessages = async () => {
    if (!conversation?.id || isLoading || !hasMore) return;

    setIsLoading(true);
    try {
      // 获取当前最早的消息的创建时间
      const earliestMessage = messages[0];
      if (!earliestMessage) {
        setHasMore(false);
        return;
      }

      // 加载比当前最早消息更早的10条消息
      const olderMessages = await db.messages
        .where('conversation_id')
        .equals(conversation.id)
        .and((item) => (item.created_at || 0) < (earliestMessage.created_at || 0))
        .reverse()
        .limit(10)
        .sortBy('created_at');

      if (olderMessages.length === 0) {
        setHasMore(false);
        return;
      }

      // 记录当前滚动位置
      const scrollArea = scrollAreaRef.current;
      const scrollHeight = scrollArea?.scrollHeight || 0;

      // 更新消息列表，将新加载的消息添加到列表前面
      setMessages((prev: Message[]) => [...olderMessages.reverse(), ...prev]);

      // 恢复滚动位置，保持用户当前查看的位置不变
      if (scrollArea) {
        const newScrollHeight = scrollArea.scrollHeight;
        scrollArea.scrollTop = newScrollHeight - scrollHeight;
      }

      setHasMore(olderMessages.length === 10);
    } catch (error) {
      console.error('加载更多消息失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理滚动事件，检测是否滚动到顶部
  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop } = event.currentTarget;

    // 当滚动到顶部时，加载更多消息
    if (scrollTop === 0 && !isLoading && hasMore) {
      loadMoreMessages();
    }
  };

  // 滚动到底部
  const scrollToBottom = () => {
    if (scrollAreaRef.current) {
      // 使用 requestAnimationFrame 确保在 DOM 更新后执行滚动
      requestAnimationFrame(() => {
        const scrollArea = scrollAreaRef.current;
        if (scrollArea) {
          scrollArea.scrollTop = scrollArea.scrollHeight;
        }
      });
    }
  };

  // 将滚动函数注册到 context 中，供 SSE 事件处理使用
  useEffect(() => {
    if (scrollToBottomRef) {
      scrollToBottomRef.current = scrollToBottom;
    }
    // 清理函数
    return () => {
      if (scrollToBottomRef) {
        scrollToBottomRef.current = null;
      }
    };
  }, [scrollToBottomRef]);

  // 监听消息变化，当有新消息或消息内容更新时自动滚动到底部
  useEffect(() => {
    const shouldScrollToBottom = () => {
      // 消息数量增加时滚动到底部（新消息）
      if (messages.length > prevMessagesLengthRef.current) {
        return true;
      }

      // 检查最后一条消息的内容是否发生变化（SSE事件更新）
      if (messages.length > 0) {
        const lastMessage = messages[messages.length - 1];
        if (lastMessage?.role === 'assistant') {
          // 将内容块转换为字符串进行比较
          const currentContent = lastMessage.content.map((block) => (block.type === 'text' ? block.content : '')).join('');

          // 如果内容发生变化，则滚动到底部
          if (currentContent !== prevLastMessageContentRef.current) {
            prevLastMessageContentRef.current = currentContent;
            return true;
          }
        }
      }

      return false;
    };

    if (shouldScrollToBottom()) {
      scrollToBottom();
    }

    prevMessagesLengthRef.current = messages.length;
  }, [messages]);

  return {
    isLoading,
    hasMore,
    scrollAreaRef,
    handleScroll,
    loadMoreMessages,
    loadRecentMessages,
    scrollToBottom,
  };
};
