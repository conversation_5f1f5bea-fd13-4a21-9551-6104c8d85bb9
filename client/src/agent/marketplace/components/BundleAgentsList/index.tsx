import React from 'react';
import { Stack, Group, Avatar, Text, Button, Skeleton } from '@mantine/core';
import { Link } from 'react-router-dom';
import { useTheme } from '~/core/features/mantine';
import { AgentDetail, AgentType } from '../../schemas';
import { useChatContext } from '~/agent/chat/contexts/ChatContext';
import { RiRobot2Line } from 'react-icons/ri';
import { ossUrl } from '~/core/utils';

interface BundleAgentsListProps {
  /**
   * 捆绑包中的智能体列表
   */
  agents: AgentDetail[];
  /**
   * 是否正在加载
   */
  isLoading?: boolean;
  /**
   * 是否为深色主题
   */
  isDark?: boolean;
}

/**
 * 捆绑包智能体列表组件
 * 用于显示捆绑包中包含的智能体，每个智能体后有开始聊天按钮
 */
const BundleAgentsList: React.FC<BundleAgentsListProps> = ({
  agents,
  isLoading = false,
  isDark = false
}) => {
  const { actualColorScheme } = useTheme();
  const isDarkTheme = isDark || actualColorScheme === 'dark';
  const { setAgent, setConversation, setMessages } = useChatContext();

  // 处理开始聊天
  const handleStartChat = (agent: AgentDetail) => {
    setAgent(agent);
    setConversation(undefined);
    setMessages([]);
  };

  if (isLoading) {
    return (
      <Stack gap="md">
        {[1, 2, 3].map((index) => (
          <Group key={index} justify="space-between" p="md" style={{
            borderRadius: '8px',
            border: `1px solid ${isDarkTheme ? '#374151' : '#e5e7eb'}`,
            backgroundColor: isDarkTheme ? '#1f2937' : '#ffffff',
          }}>
            <Group gap="md">
              <Skeleton height={40} circle />
              <div>
                <Skeleton height={16} width={120} mb={4} />
                <Skeleton height={12} width={200} />
              </div>
            </Group>
            <Skeleton height={32} width={80} />
          </Group>
        ))}
      </Stack>
    );
  }

  if (!agents || agents.length === 0) {
    return (
      <Text c="dimmed" ta="center" py="xl">
        暂无智能体
      </Text>
    );
  }

  return (
    <Stack gap="md">
      {agents.map((agent) => (
        <Group
          key={agent.id}
          justify="space-between"
          p="md"
          style={{
            borderRadius: '8px',
            border: `1px solid ${isDarkTheme ? '#374151' : '#e5e7eb'}`,
            backgroundColor: isDarkTheme ? '#1f2937' : '#ffffff',
            transition: 'all 0.2s ease',
          }}
          className="hover:shadow-md"
        >
          <Group gap="md">
            {/* 智能体头像 */}
            <Avatar
              size={40}
              src={agent.icon ? ossUrl(agent.icon) : null}
              bg="rgba(73, 81, 235, 1)"
              color="white"
            >
              <RiRobot2Line size={20} />
            </Avatar>

            {/* 智能体信息 */}
            <div>
              <Text fw={600} size="sm" c={isDarkTheme ? 'white' : 'dark'}>
                {agent.name}
              </Text>
              <Text size="xs" c="dimmed" lineClamp={1} maw={300}>
                {agent.description}
              </Text>
            </div>
          </Group>

          {/* 开始聊天按钮 */}
          {agent.mode === AgentType.WORKFLOW ? (
            // 工作流类型，显示运行按钮
            <Link to={`/workflow/${agent.id}`}>
              <Button
                size="sm"
                variant="filled"
                color="blue"
                onClick={() => handleStartChat(agent)}
              >
                运行
              </Button>
            </Link>
          ) : (
            // 其他类型，显示开始聊天按钮
            <Link to={`/chat/${agent.id}`}>
              <Button
                size="sm"
                variant="filled"
                color="blue"
                onClick={() => handleStartChat(agent)}
              >
                开始聊天
              </Button>
            </Link>
          )}
        </Group>
      ))}
    </Stack>
  );
};

export default BundleAgentsList;
