import { <PERSON>ete<PERSON><PERSON><PERSON>, EditButton, List, ShowButton, useTable } from "@refinedev/antd";
import { Button, Form, Input, Space, Table, Tag } from "antd";
import React from "react";
import type { BaseRecord } from "@refinedev/core";
import { SearchForm } from "@/common/components";
import { Course } from "@/course";
import { entityKey } from "@/course";

const CourseList: React.FC = () => {
  const { tableProps, searchFormProps } = useTable<Course>({
    resource: entityKey,
    syncWithLocation: false,
    defaultSetFilterBehavior: "replace",
    sorters: {
      initial: [
        {
          field: "created_at",
          order: "desc",
        },
      ],
    },
  });

  return (
    <List>
      <SearchForm searchFormProps={searchFormProps}>
        <Form.Item name="title" label="课程标题">
          <Input placeholder="请输入课程标题" />
        </Form.Item>
        <Form.Item name="instructor" label="导师">
          <Input placeholder="请输入导师名称" />
        </Form.Item>
      </SearchForm>

      <Table {...tableProps} rowKey="id">
        <Table.Column
          title="ID"
          dataIndex="id"
          width={80}
        />
        <Table.Column
          title="课程标题"
          dataIndex="title"
          width={200}
        />
        <Table.Column
          title="导师"
          dataIndex="instructor"
          width={120}
          render={(instructor) => instructor || '-'}
        />
        <Table.Column
          title="描述"
          dataIndex="description"
          width={300}
          render={(description) => {
            if (!description) return '-';
            return description.length > 50
              ? `${description.substring(0, 50)}...`
              : description;
          }}
        />
        <Table.Column
          title="标签"
          dataIndex="tags"
          width={200}
          render={(tags) => {
            if (!tags || tags.length === 0) return '-';
            return (
              <>
                {Array.isArray(tags) && tags.map((tag, index) => (
                  <Tag key={index} color="blue">{tag}</Tag>
                ))}
              </>
            );
          }}
        />
        <Table.Column
          title="创建时间"
          dataIndex="created_at"
          width={180}
          render={(date) => date && new Date(date).toLocaleString()}
        />
        <Table.Column
          title="操作"
          dataIndex="actions"
          width={150}
          render={(_, record: BaseRecord) => (
            <Space>
              <EditButton hideText size="small" recordItemId={record.id} />
              <DeleteButton hideText size="small" recordItemId={record.id} />
              <ShowButton hideText size="small" recordItemId={record.id} />
            </Space>
          )}
        />
      </Table>
    </List>
  );
}

export default CourseList;
