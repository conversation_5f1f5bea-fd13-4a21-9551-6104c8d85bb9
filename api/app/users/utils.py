"""
用户相关的工具函数
"""
from typing import List

from sqlalchemy import or_
from sqlmodel import Session, select

from billing.keys import KeyResponse
from app.db.models import Agent, Course


def populate_key_resource_names(keys: List[KeyResponse], session: Session,user_id: int):
    """
    为密钥列表填充对应的资源名称

    Args:
        keys: 密钥列表
    """

    for key in keys:

        scope_names = []
        # 根据服务代码查询对应的资源名称
        if key.service_code == "AGC":
            # 智能体服务
            # 如果scope为空，表示可用域为所有智能体/工作流
            if not key.scope:
                statement = select(Agent).where(
                or_(Agent.is_public == True, Agent.owner_id.is_(None), Agent.owner_id == user_id)
                )
                agents = session.exec(statement).all()
                scope_names = [agent.name for agent in agents]
                key.scope =  [agent.id for agent in agents]
            else:
            # 有指定可用域
                for agent_id in key.scope:
                    agent = session.exec(
                        select(Agent).where(Agent.id == agent_id)
                    ).first()

                    if agent and agent.name:
                        scope_names.append(agent.name)

        elif key.service_code == "COU":
            # 课程服务
            for instance_id in key.scope:
                try:
                    course_id = int(instance_id)
                    course = session.exec(
                        select(Course).where(Course.id == course_id)
                    ).first()

                    if course and course.title:
                        scope_names.append(course.title)
                    else:
                        # 如果找不到名称，就使用ID
                        scope_names.append(instance_id)
                except ValueError:
                    # 如果ID转换失败，使用原始ID
                    scope_names.append(instance_id)

        # 将多个范围名称以逗号连接
        key.scope_names = scope_names
