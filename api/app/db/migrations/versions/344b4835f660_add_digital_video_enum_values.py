"""add_digital_video_enum_values

Revision ID: 344b4835f660
Revises: b17d5707df84
Create Date: 2025-06-04 21:58:24.751548

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '344b4835f660'
down_revision: Union[str, None] = 'b17d5707df84'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 添加 DIGITAL_VIDEO 到 product_type_enum
    op.execute("ALTER TYPE product_type_enum ADD VALUE IF NOT EXISTS 'DIGITAL_VIDEO'")

    # 添加 DIGITAL_VIDEO 到 payment_plan_scope_type_enum
    op.execute("ALTER TYPE payment_plan_scope_type_enum ADD VALUE IF NOT EXISTS 'DIGITAL_VIDEO'")


def downgrade() -> None:
    # PostgreSQL不支持删除枚举值，所以这里留空
    # 如果需要回滚，需要重新创建枚举类型
    pass
