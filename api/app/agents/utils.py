from sqlmodel import select
import wrapt
import json
from billing import HkJingXiuBilling
from billing.keys import KeyConsumeRequest, CurrencyType
from dify import AdminClient, Dify
from dify.app.schemas import MessageEndEvent
from fastapi import HTTPException
from httpx import AsyncClient
import lark_oapi as lark
from lark_oapi.api.bitable.v1 import *
from lark_oapi.api.drive.v1 import *
import requests

from app.core import chat_logger, settings
from app.core.exceptions import (
    NoChatPermissionException,
    UnauthorizedAccessException,
    DatabaseSessionUnavailableException,
    MissingAgentIdException,
)
from app.db.models import User, Agent

admin_client = AdminClient(settings.dify_base_url, settings.dify_admin_key)
dify = Dify(admin_client)


def require_chat_permission(
    currenices: list[CurrencyType] = [
        CurrencyType.CNY,
        CurrencyType.LTC,
        CurrencyType.UTS,
    ]
):
    """
    装饰器：检查用户是否拥有智能体的密钥权限

    此装饰器会检查当前用户是否在商户平台上拥有指定智能体的使用权限。
    通过查询用户可用密钥来确认用户是否有权限使用该智能体。

    用法:
    @require_chat_permission()
    async def some_function(agent_id: str, current_user: User, session: Session):
        # 函数实现

    :raises:
        NoChatPermissionException: 如果用户没有权限使用该智能体
        UnauthorizedAccessException: 如果用户未授权访问
        DatabaseSessionUnavailableException: 如果数据库会话不可用
        MissingAgentIdException: 如果缺少智能体ID参数
    """

    @wrapt.decorator
    async def wrapper(wrapped, instance, args, kwargs):
        # 从依赖注入获取当前用户、会话和智能体ID
        current_user: User = kwargs.get("current_user")
        if not current_user:
            raise UnauthorizedAccessException()

        session = kwargs.get("session")
        if not session:
            raise DatabaseSessionUnavailableException()

        # 先尝试获取agent_id参数
        agent_id = kwargs.get("agent_id")

        if not agent_id:
            raise MissingAgentIdException()

        # 检查用户是否拥有该智能体的密钥
        chat_logger.info(f"检查用户 {current_user.id} 对智能体 {agent_id} 的权限")
        billing_client = HkJingXiuBilling(
            base_url=settings.hkjx_base_url, admin_key=settings.hkjx_appsecret
        )

        # 首先检查用户是否有直接针对该智能体的密钥
        available_keys = []
        for currency in currenices:
            keys = await billing_client.keys.list_available_keys(
                user_id=str(current_user.id),
                service_code="AGC",
                scope=[agent_id],
                currency_type=currency.value,
            )
            available_keys.extend(keys.keys)
            if len(keys.keys) > 0:
                break
        if len(available_keys) == 0:
            # 使用自定义业务异常替代HTTPException
            raise NoChatPermissionException(agent_id=agent_id)

        # 用户有权限，继续执行原函数
        return await wrapped(*args, **kwargs)

    return wrapper


async def transcribe_audio(file_path: str) -> str:
    """将音频文件转换为文本"""
    try:
        async with AsyncClient() as client:
            with open(file_path, "rb") as audio_file:
                files = {"file": ("audio.mp3", audio_file, "audio/mpeg")}
                response = await client.post(
                    "https://difytools.cruldra.cn/audios/transcribe",
                    files=files,
                    headers={"accept": "application/json"},
                )

            if response.status_code != 200:
                chat_logger.error(f"语音转文本失败: {response.text}")
                raise HTTPException(
                    status_code=response.status_code, detail="语音转文本服务异常"
                )

            return response.json()["data"]["text"]

    except Exception as e:
        chat_logger.exception(f"语音转文本时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail="语音转文本失败")


async def consume_key(user_id: int, agent_id: str, message_end_event: MessageEndEvent):
    chat_logger.info(f"上报用户 {user_id} 使用智能体 {agent_id} 的令牌消耗")
    billing_client = HkJingXiuBilling(
        base_url=settings.hkjx_base_url, admin_key=settings.hkjx_appsecret
    )

    # 首先尝试获取直接针对该智能体的密钥
    keys = await billing_client.keys.list_available_keys(
        user_id=str(user_id),
        service_code="AGC",
        scope=[agent_id],
    )
    if len(keys.keys) == 0:
        raise NoChatPermissionException(agent_id=agent_id)
    key = keys.keys[0]

    # 消费密钥
    chat_logger.info(f"使用密钥 {key.id} 消费智能体 {agent_id}")
    await billing_client.keys.consume_key(
        KeyConsumeRequest(
            key_id=key.id,
            scope=[agent_id],
            details=message_end_event.metadata.usage.model_dump(),
        )
    )


def get_agent_features(agent_detail):
    """
    根据智能体详情获取支持的功能列表

    :param agent_detail: 智能体详情对象
    :return: 功能列表，例如 ["网页搜索", "DALL·E 图片", "代码解译器和数据分析"]
    """
    # 这里是示例实现，实际逻辑可以根据智能体的配置、模型等信息来确定
    features = []

    return ["功能1", "功能2", "功能3"]


async def check_user_has_purchased_agent(user: User, agent: Agent, session) -> bool:
    """
    检查用户是否已购买智能体

    此函数通过查询用户可用密钥来确认用户是否已购买指定智能体。
    如果用户有针对该智能体的可用密钥，则表示已购买。
    对于捆绑包类型的智能体，会检查用户是否有针对该捆绑包中任一智能体的密钥。

    Args:
        user: 用户对象
        agent: 智能体对象
        session: 数据库会话

    Returns:
        bool: 如果用户已购买该智能体，返回True；否则返回False
    """
    chat_logger.debug(f"检查用户 {user.id} 是否已购买智能体 {agent.id}")

    # 创建billing客户端
    billing_client = HkJingXiuBilling(
        base_url=settings.hkjx_base_url, admin_key=settings.hkjx_appsecret
    )

    # 首先检查用户是否有直接针对该智能体的密钥
    keys = await billing_client.keys.list_available_keys(
        user_id=str(user.id),
        service_code="AGC",
        scope=[agent.id],
    )

    if len(keys.keys) > 0:
        chat_logger.debug(f"用户 {user.id} 有针对智能体 {agent.id} 的直接密钥")
        return True
    return False


class FeishuDocument(object):
    """
    飞书云文档智能体
    """

    def __init__(self, app_id: str, app_secret: str):
        self.app_id = app_id
        self.app_secret = app_secret
        self.token = self.__get_token__()

    def __get_token__(self) -> str:
        """
        获取飞书token
        """
        url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
        headers = {"Content-Type": "application/json"}
        payload = {"app_id": self.app_id, "app_secret": self.app_secret}
        response = requests.post(url, headers=headers, json=payload)
        if response.status_code == 200:
            return response.json().get("tenant_access_token")
        else:
            raise Exception(f"Failed to get access token: {response.text}")

    def __get_root_folder_token__(self) -> str:
        """
        获取根文件夹token
        """
        url = "https://open.feishu.cn/open-apis/drive/explorer/v2/root_folder/meta"
        payload = {}
        headers = {"Authorization": f"Bearer {self.token}"}
        response = requests.request("GET", url, headers=headers, data=payload)
        return response.json().get("data").get("token")

    async def create_document(self, title: str, content: str = "") -> dict:
        """
        创建飞书云文档并添加内容

        Args:
            title: 文档标题
            content: 文档内容

        Returns:
            dict: 包含文档信息的字典，包括document_id和url
        """
        # 创建空文档
        url = "https://open.feishu.cn/open-apis/docx/v1/documents"
        headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }

        payload = {
            "title": title,
            "folder_token": self.__get_root_folder_token__()
        }

        response = requests.post(url, headers=headers, json=payload)

        if response.status_code != 200:
            raise Exception(f"Failed to create document: {response.text}")

        doc_data = response.json().get("data", {})
        document_id = doc_data.get("document", {}).get("document_id")

        if not document_id:
            raise Exception("Failed to get document_id from response")

        # 如果有内容，直接写入文档
        if content:
            self.write_content_to_document(document_id, content)

        # 尝试设置文档为公开可访问（如果失败也不影响主要功能）
        try:
            self.__make_document_public_simple__(document_id)
        except Exception as e:
            chat_logger.warning(f"Failed to make document public, but document was created successfully: {e}")

        # 构造文档URL
        doc_url = f"https://bytedance.feishu.cn/docx/{document_id}"

        return {
            "document_id": document_id,
            "url": doc_url,
            "title": title
        }

    def write_content_to_document(self, document_id: str, content: str):
        """
        将内容写入文档 - 使用正确的飞书API方法

        Args:
            document_id: 文档ID
            content: 要写入的内容
        """
        try:
            # 第一步：获取文档信息，找到body块
            doc_url = f"https://open.feishu.cn/open-apis/docx/v1/documents/{document_id}"
            headers = {
                "Authorization": f"Bearer {self.token}",
                "Content-Type": "application/json"
            }

            doc_response = requests.get(doc_url, headers=headers)
            if doc_response.status_code != 200:
                chat_logger.error(f"Failed to get document info: {doc_response.text}")
                return

            doc_data = doc_response.json()
            chat_logger.info(f"Document data: {doc_data}")

            # 第二步：获取文档的所有块
            blocks_url = f"https://open.feishu.cn/open-apis/docx/v1/documents/{document_id}/blocks"
            blocks_response = requests.get(blocks_url, headers=headers)

            if blocks_response.status_code != 200:
                chat_logger.error(f"Failed to get document blocks: {blocks_response.text}")
                return

            blocks_data = blocks_response.json()
            chat_logger.info(f"Blocks data: {blocks_data}")

            # 找到第一个可以添加内容的块（通常是页面块）
            page_block_id = None
            if "data" in blocks_data and "items" in blocks_data["data"]:
                for block in blocks_data["data"]["items"]:
                    if block.get("block_type") == 1:  # 页面块
                        page_block_id = block.get("block_id")
                        break

            if not page_block_id:
                chat_logger.error("No suitable block found to add content")
                return

            # 第三步：向页面块添加格式化的文本块
            add_content_url = f"https://open.feishu.cn/open-apis/docx/v1/documents/{document_id}/blocks/{page_block_id}/children"

            # 解析Markdown格式并转换为飞书块格式
            formatted_blocks = self._parse_markdown_to_feishu_elements(content)

            payload = {
                "children": formatted_blocks
            }

            response = requests.post(add_content_url, headers=headers, json=payload)

            if response.status_code == 200:
                chat_logger.info(f"Successfully wrote content to document {document_id}")
            else:
                chat_logger.error(f"Failed to write content to document: {response.text}")

        except Exception as e:
            chat_logger.error(f"Error writing content to document: {e}")

    def __make_document_public_simple__(self, document_id: str):
        """
        简化的文档公开设置

        Args:
            document_id: 文档ID
        """
        try:
            # 使用正确的权限设置API - 注意URL中使用token而不是document_id
            # 并且需要在URL中指定type参数
            url = f"https://open.feishu.cn/open-apis/drive/v1/permissions/{document_id}/public?type=docx"
            headers = {
                "Authorization": f"Bearer {self.token}",
                "Content-Type": "application/json"
            }

            # 简化的权限设置参数
            payload = {
                "external_access_entity": "anyone_can_view",
                "security_entity": "anyone_can_view",
                "comment_entity": "anyone_can_view",
                "share_entity": "anyone",
                "link_share_entity": "anyone_editable",
                "invite_external": True
            }

            response = requests.patch(url, headers=headers, json=payload)

            if response.status_code == 200:
                chat_logger.info(f"Successfully made document {document_id} public")
            else:
                chat_logger.warning(f"Failed to make document public: {response.text}")
                # 即使权限设置失败，文档仍然创建成功，用户可以手动分享

        except Exception as e:
            chat_logger.warning(f"Error making document public: {e}")

    def _parse_markdown_to_feishu_elements(self, content: str) -> list:
        """
        将Markdown格式的文本转换为飞书文档的多个块元素

        Args:
            content: Markdown格式的文本

        Returns:
            list: 飞书文档块元素列表
        """
        import re

        blocks = []

        # 分割文本为段落
        paragraphs = content.split('\n\n')

        for paragraph in paragraphs:
            if not paragraph.strip():
                continue

            lines = paragraph.strip().split('\n')

            # 检查是否为列表段落
            if any(line.strip().startswith(('• ', '* ')) for line in lines):
                # 处理列表
                for line in lines:
                    if line.strip().startswith(('• ', '* ')):
                        line_content = line.strip()[2:]  # 移除列表标记
                        formatted_elements = self._parse_line_formatting(line_content)

                        # 创建列表项块
                        blocks.append({
                            "block_type": 2,  # 文本块
                            "text": {
                                "elements": [
                                    {
                                        "text_run": {
                                            "content": "• "
                                        }
                                    }
                                ] + formatted_elements
                            }
                        })
            else:
                # 处理普通段落
                paragraph_elements = []
                for i, line in enumerate(lines):
                    if line.strip():
                        paragraph_elements.extend(self._parse_line_formatting(line))
                        if i < len(lines) - 1:  # 不是最后一行
                            paragraph_elements.append({
                                "text_run": {
                                    "content": "\n"
                                }
                            })

                if paragraph_elements:
                    blocks.append({
                        "block_type": 2,  # 文本块
                        "text": {
                            "elements": paragraph_elements
                        }
                    })

        return blocks

    def _parse_line_formatting(self, line: str) -> list:
        """
        解析单行文本中的格式化标记，支持加粗、斜体、代码等

        Args:
            line: 单行文本

        Returns:
            list: 格式化元素列表
        """
        import re

        elements = []

        # 定义格式化模式（按优先级排序）
        patterns = [
            (r'\*\*(.*?)\*\*', {'bold': True}),  # 加粗 **text**
            (r'\*(.*?)\*', {'italic': True}),    # 斜体 *text*
            (r'`(.*?)`', {'code': True}),        # 代码 `code`
        ]

        # 收集所有匹配项
        matches = []
        for pattern, style in patterns:
            for match in re.finditer(pattern, line):
                matches.append({
                    'start': match.start(),
                    'end': match.end(),
                    'content': match.group(1),
                    'style': style,
                    'full_match': match.group(0)
                })

        # 按位置排序，处理重叠
        matches.sort(key=lambda x: (x['start'], -x['end']))

        # 去除重叠的匹配项（优先保留较长的）
        filtered_matches = []
        for match in matches:
            overlap = False
            for existing in filtered_matches:
                if (match['start'] < existing['end'] and match['end'] > existing['start']):
                    overlap = True
                    break
            if not overlap:
                filtered_matches.append(match)

        # 重新排序
        filtered_matches.sort(key=lambda x: x['start'])

        last_end = 0

        for match in filtered_matches:
            # 添加匹配前的普通文本
            if match['start'] > last_end:
                normal_text = line[last_end:match['start']]
                if normal_text:
                    elements.append({
                        "text_run": {
                            "content": normal_text
                        }
                    })

            # 添加格式化文本
            style_dict = {}
            if match['style'].get('bold'):
                style_dict['bold'] = True
            if match['style'].get('italic'):
                style_dict['italic'] = True
            if match['style'].get('code'):
                # 代码样式在飞书中可能需要特殊处理
                style_dict['code'] = True

            element = {
                "text_run": {
                    "content": match['content']
                }
            }

            if style_dict:
                element["text_run"]["text_element_style"] = style_dict

            elements.append(element)
            last_end = match['end']

        # 添加剩余的普通文本
        if last_end < len(line):
            remaining_text = line[last_end:]
            if remaining_text:
                elements.append({
                    "text_run": {
                        "content": remaining_text
                    }
                })

        # 如果没有找到任何格式化标记，直接添加整行
        if not elements:
            elements.append({
                "text_run": {
                    "content": line
                }
            })

        return elements
